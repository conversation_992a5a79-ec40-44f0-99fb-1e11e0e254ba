<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('first_name', 50)->after('email');
            $table->string('last_name', 50)->after('first_name');
            $table->boolean('is_active')->default(true)->after('password');
            $table->timestamp('last_login')->nullable()->after('is_active');
            $table->integer('failed_login_attempts')->default(0)->after('last_login');
            $table->timestamp('locked_until')->nullable()->after('failed_login_attempts');

            $table->index('is_active');
            $table->index('last_login');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['is_active']);
            $table->dropIndex(['last_login']);
            $table->dropColumn(['first_name', 'last_name', 'is_active', 'last_login', 'failed_login_attempts', 'locked_until']);
        });
    }
};
