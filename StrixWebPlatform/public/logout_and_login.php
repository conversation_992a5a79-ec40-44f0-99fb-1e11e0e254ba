<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

echo "<h1>Logout and Login</h1>";

// Force logout
$app->logout();
echo "<p>✅ Logged out successfully</p>";

echo "<h2>Please login again:</h2>";
echo "<p><a href='/login'>Go to Login Page</a></p>";
echo "<p>Use credentials: admin / admin123</p>";

echo "<p>After logging in, the fresh permissions should be loaded from the database.</p>";
