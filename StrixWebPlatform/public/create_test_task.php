<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\Task;
use Strix\ERP\Models\TaskType;
use Strix\ERP\Models\TaskStatus;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

echo "<h1>Create Test Task</h1>";

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

$currentUser = $app->getCurrentUser();

try {
    // Get first task type and status
    $taskTypes = TaskType::getActiveTypes();
    $taskStatuses = TaskStatus::getActiveStatuses();
    
    if (empty($taskTypes)) {
        echo "<p style='color: red;'>No task types found!</p>";
        exit;
    }
    
    if (empty($taskStatuses)) {
        echo "<p style='color: red;'>No task statuses found!</p>";
        exit;
    }
    
    echo "<h2>Available Task Types:</h2>";
    foreach ($taskTypes as $type) {
        echo "<li>{$type->name} (ID: {$type->id})</li>";
    }
    
    echo "<h2>Available Task Statuses:</h2>";
    foreach ($taskStatuses as $status) {
        echo "<li>{$status->name} (ID: {$status->id})</li>";
    }
    
    // Create a test task
    $task = new Task();
    $task->title = 'Тестова задача ' . date('Y-m-d H:i:s');
    $task->description = 'Това е тестова задача, създадена автоматично за тестване на системата.';
    $task->task_type_id = $taskTypes[0]->id;
    $task->status_id = $taskStatuses[0]->id;
    $task->priority = 'normal';
    $task->progress = 0;
    $task->created_by = $currentUser['id'];
    $task->due_date = date('Y-m-d', strtotime('+7 days'));
    
    if ($task->save()) {
        echo "<h2>✅ Test task created successfully!</h2>";
        echo "<p>Task ID: {$task->id}</p>";
        echo "<p>Title: {$task->title}</p>";
        echo "<p>Type: {$taskTypes[0]->name}</p>";
        echo "<p>Status: {$taskStatuses[0]->name}</p>";
        echo "<p>Priority: {$task->priority}</p>";
        echo "<p>Due Date: {$task->due_date}</p>";
        
        echo "<h2>Actions:</h2>";
        echo "<p><a href='/admin/tasks'>View All Tasks</a></p>";
        echo "<p><a href='/admin/tasks/{$task->id}'>View This Task</a></p>";
        echo "<p><a href='/admin/tasks/create'>Create Another Task</a></p>";
        
    } else {
        echo "<p style='color: red;'>❌ Failed to create test task</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
