<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\UserNextcloudSettings;
use Strix\ERP\Core\Database;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$currentUser = $app->getCurrentUser();

echo "<!DOCTYPE html><html><head><title>Debug Save Settings</title></head><body>";
echo "<h1>🔧 Debug Save Settings</h1>";

echo "<h2>Current User Info</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><td><strong>User ID:</strong></td><td>" . $currentUser['id'] . "</td></tr>";
echo "<tr><td><strong>Username:</strong></td><td>" . htmlspecialchars($currentUser['username']) . "</td></tr>";
echo "<tr><td><strong>Full Name:</strong></td><td>" . htmlspecialchars($currentUser['full_name']) . "</td></tr>";
echo "</table>";

echo "<h2>Database Connection Test</h2>";
try {
    $testQuery = "SELECT COUNT(*) as count FROM user_nextcloud_settings";
    $result = Database::fetchOne($testQuery);
    echo "<p style='color: green;'>✅ Database connection OK. Current records: " . $result['count'] . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>Test Data</h2>";
$testData = [
    'server_url' => 'https://demo.nextcloud.com',
    'username' => 'testuser',
    'password' => 'testpass',
    'verify_ssl' => true,
    'timeout' => 30,
    'default_folder' => '/ERP_Test',
    'auto_create_folders' => true
];

echo "<pre>" . print_r($testData, true) . "</pre>";

echo "<h2>Step-by-Step Save Process</h2>";

try {
    echo "<h3>Step 1: Create UserNextcloudSettings instance</h3>";
    $settings = new UserNextcloudSettings();
    echo "<p style='color: green;'>✅ Instance created</p>";
    
    echo "<h3>Step 2: Set basic properties</h3>";
    $settings->user_id = $currentUser['id'];
    $settings->server_url = $testData['server_url'];
    $settings->username = $testData['username'];
    $settings->verify_ssl = $testData['verify_ssl'];
    $settings->timeout = $testData['timeout'];
    $settings->default_folder = $testData['default_folder'];
    $settings->auto_create_folders = $testData['auto_create_folders'];
    $settings->is_active = true;
    echo "<p style='color: green;'>✅ Basic properties set</p>";
    
    echo "<h3>Step 3: Set encrypted password</h3>";
    try {
        $settings->setEncryptedPassword($testData['password']);
        echo "<p style='color: green;'>✅ Password encrypted: " . htmlspecialchars($settings->password) . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Password encryption failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h3>Step 4: Check object state before save</h3>";
    echo "<pre>";
    foreach (get_object_vars($settings) as $prop => $value) {
        if ($prop === 'password') {
            echo "$prop: " . (empty($value) ? 'NULL' : '[ENCRYPTED]') . "\n";
        } else {
            echo "$prop: " . var_export($value, true) . "\n";
        }
    }
    echo "</pre>";
    
    echo "<h3>Step 5: Test database deactivation query</h3>";
    try {
        $deactivateQuery = "UPDATE user_nextcloud_settings SET is_active = 0 WHERE user_id = ?";
        $stmt = Database::query($deactivateQuery, [$currentUser['id']]);
        echo "<p style='color: green;'>✅ Deactivation query executed (affected rows: " . $stmt->rowCount() . ")</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Deactivation query failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<h3>Step 6: Attempt to save</h3>";
    try {
        $result = $settings->save();
        echo "<p style='color: green;'>✅ Save method returned: " . var_export($result, true) . "</p>";
        echo "<p style='color: green;'>✅ Settings ID after save: " . $settings->id . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Save failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p style='color: red;'>Error trace:</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    }
    
    echo "<h3>Step 7: Verify database record</h3>";
    try {
        $checkQuery = "SELECT * FROM user_nextcloud_settings WHERE user_id = ?";
        $records = Database::fetchAll($checkQuery, [$currentUser['id']]);
        echo "<p style='color: green;'>✅ Found " . count($records) . " records for user</p>";
        if (!empty($records)) {
            echo "<pre>" . print_r($records, true) . "</pre>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Verification query failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ General error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<h2>Test createOrUpdate Method</h2>";
try {
    echo "<h3>Testing UserNextcloudSettings::createOrUpdate()</h3>";
    $settings = UserNextcloudSettings::createOrUpdate($currentUser['id'], $testData);
    echo "<p style='color: green;'>✅ createOrUpdate succeeded</p>";
    echo "<p>Settings ID: " . $settings->id . "</p>";
    echo "<p>Server URL: " . htmlspecialchars($settings->server_url) . "</p>";
    echo "<p>Username: " . htmlspecialchars($settings->username) . "</p>";
    echo "<p>Has password: " . (!empty($settings->password) ? 'Yes' : 'No') . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ createOrUpdate failed: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<h2>Final Database State</h2>";
try {
    $finalQuery = "SELECT * FROM user_nextcloud_settings";
    $allRecords = Database::fetchAll($finalQuery);
    echo "<p style='color: green;'>✅ Total records in database: " . count($allRecords) . "</p>";
    if (!empty($allRecords)) {
        echo "<pre>" . print_r($allRecords, true) . "</pre>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Final query failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</body></html>";
?>
