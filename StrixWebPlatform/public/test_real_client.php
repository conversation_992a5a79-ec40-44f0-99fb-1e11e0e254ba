<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Controllers\StorageController;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'admin',
    'role' => 'admin',
    'permissions' => ['storage.view', 'storage.download', 'storage.edit', 'storage.delete', 'storage.upload', 'storage.create']
];

// Force real client
$_GET['force_real'] = true;

echo "<!DOCTYPE html><html><head><title>Test Real Nextcloud Client</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    .warning { color: orange; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    .debug-info { background: #e8f4fd; padding: 10px; border-radius: 3px; margin: 10px 0; }
</style></head><body>";

echo "<h1>🌐 Test Real Nextcloud Client</h1>";
echo "<p class='info'>This test forces the use of the real NextcloudClient instead of the mock.</p>";

// Test 1: Check user settings first
echo "<div class='test'>";
echo "<h2>Test 1: User Settings Check</h2>";
try {
    $userSettings = \Strix\ERP\Models\UserNextcloudSettings::getActiveForUser(1);

    if ($userSettings) {
        echo "<p class='success'>✅ Found user Nextcloud settings</p>";
        echo "<div class='debug-info'>";
        echo "<h3>User Settings:</h3>";
        echo "<p><strong>Server URL:</strong> " . htmlspecialchars($userSettings->server_url) . "</p>";
        echo "<p><strong>Username:</strong> " . htmlspecialchars($userSettings->username) . "</p>";
        echo "<p><strong>Has Password:</strong> " . (!empty($userSettings->password) ? 'YES' : 'NO') . "</p>";
        echo "<p><strong>Verify SSL:</strong> " . ($userSettings->verify_ssl ? 'YES' : 'NO') . "</p>";
        echo "<p><strong>Timeout:</strong> " . $userSettings->timeout . "s</p>";
        echo "</div>";

        // Test connection
        $testResult = $userSettings->testConnection();
        if ($testResult['success']) {
            echo "<p class='success'>✅ User settings connection test: SUCCESS</p>";
        } else {
            echo "<p class='error'>❌ User settings connection test: FAILED - " . htmlspecialchars($testResult['error']) . "</p>";
        }
    } else {
        echo "<p class='warning'>⚠️ No user Nextcloud settings found</p>";
    }

} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking user settings: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// Test 2: Get client type
echo "<div class='test'>";
echo "<h2>Test 2: Client Type</h2>";
try {
    $controller = new StorageController();
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('getNextcloudClient');
    $method->setAccessible(true);
    $client = $method->invoke($controller);

    $clientType = get_class($client);
    echo "<p><strong>Client type:</strong> " . $clientType . "</p>";

    if ($clientType === 'Strix\ERP\Services\NextcloudClient') {
        echo "<p class='success'>✅ Using real NextcloudClient</p>";

        // Get debug info if available
        if (method_exists($client, 'getDebugInfo')) {
            $debugInfo = $client->getDebugInfo();
            echo "<div class='debug-info'>";
            echo "<h3>Client Configuration:</h3>";
            foreach ($debugInfo as $key => $value) {
                echo "<p><strong>" . ucfirst(str_replace('_', ' ', $key)) . ":</strong> " . htmlspecialchars($value) . "</p>";
            }
            echo "</div>";
        }
    } else {
        echo "<p class='warning'>⚠️ Still using MockNextcloudClient - real client creation failed</p>";
    }

} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

if (isset($client) && get_class($client) === 'Strix\ERP\Services\NextcloudClient') {
    // Test 3: List root directory
    echo "<div class='test'>";
    echo "<h2>Test 3: List Root Directory</h2>";
    try {
        $files = $client->listDirectory('/');
        echo "<p class='success'>✅ Listed root directory successfully</p>";
        echo "<p><strong>Found:</strong> " . count($files) . " items</p>";
        
        if (!empty($files)) {
            echo "<h3>Directory Contents:</h3>";
            echo "<ul>";
            foreach (array_slice($files, 0, 10) as $file) {
                $icon = $file['is_directory'] ? '📁' : '📄';
                $size = $file['is_directory'] ? '' : ' (' . number_format($file['size']) . ' bytes)';
                echo "<li>$icon " . htmlspecialchars($file['name']) . $size . "</li>";
            }
            if (count($files) > 10) {
                echo "<li>... and " . (count($files) - 10) . " more items</li>";
            }
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error listing directory: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    echo "</div>";
    
    // Test 4: Test file operations
    echo "<div class='test'>";
    echo "<h2>Test 4: File Operations</h2>";
    
    // Find a test file
    $testFile = null;
    try {
        $files = $client->listDirectory('/');
        foreach ($files as $file) {
            if (!$file['is_directory'] && $file['size'] > 0 && $file['size'] < 5*1024*1024) { // Find a file under 5MB
                $testFile = $file;
                break;
            }
        }
        
        if ($testFile) {
            echo "<p class='info'>Testing with file: " . htmlspecialchars($testFile['name']) . " (" . number_format($testFile['size']) . " bytes)</p>";
            
            // Test exists
            $exists = $client->exists($testFile['path']);
            echo "<p class='" . ($exists ? 'success' : 'error') . "'>";
            echo ($exists ? "✅" : "❌") . " File exists: " . ($exists ? "YES" : "NO");
            echo "</p>";
            
            if ($exists) {
                // Test download
                try {
                    echo "<p class='info'>Attempting to download file...</p>";
                    $content = $client->downloadFile($testFile['path']);
                    
                    if ($content !== false) {
                        echo "<p class='success'>✅ Download successful!</p>";
                        echo "<p><strong>Downloaded size:</strong> " . number_format(strlen($content)) . " bytes</p>";
                        echo "<p><strong>Expected size:</strong> " . number_format($testFile['size']) . " bytes</p>";
                        
                        if (strlen($content) === $testFile['size']) {
                            echo "<p class='success'>✅ Size matches perfectly!</p>";
                        } else {
                            echo "<p class='warning'>⚠️ Size mismatch - possible issue</p>";
                        }
                        
                        // Show content preview for text files
                        if (strpos($testFile['content_type'], 'text/') === 0 || 
                            in_array(pathinfo($testFile['name'], PATHINFO_EXTENSION), ['txt', 'md', 'json', 'xml', 'html', 'css', 'js'])) {
                            echo "<p><strong>Content preview:</strong></p>";
                            echo "<pre>" . htmlspecialchars(substr($content, 0, 500)) . "</pre>";
                            if (strlen($content) > 500) {
                                echo "<p><em>... (truncated)</em></p>";
                            }
                        }
                        
                    } else {
                        echo "<p class='error'>❌ Download failed - returned false</p>";
                    }
                    
                } catch (Exception $e) {
                    echo "<p class='error'>❌ Download error: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }
            
        } else {
            echo "<p class='warning'>⚠️ No suitable test file found in root directory</p>";
            echo "<p class='info'>Available files:</p>";
            echo "<ul>";
            foreach ($files as $file) {
                if (!$file['is_directory']) {
                    echo "<li>" . htmlspecialchars($file['name']) . " (" . number_format($file['size']) . " bytes)</li>";
                }
            }
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error during file operations: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
    
    // Test 5: Test through controller
    echo "<div class='test'>";
    echo "<h2>Test 5: Test Through Controller</h2>";
    
    if ($testFile) {
        try {
            // Test download through controller
            $_GET['path'] = $testFile['path'];
            
            echo "<p class='info'>Testing download through StorageController...</p>";
            
            ob_start();
            $controller->download();
            $output = ob_get_contents();
            ob_end_clean();
            
            if (!empty($output)) {
                echo "<p class='success'>✅ Controller download successful!</p>";
                echo "<p><strong>Output size:</strong> " . number_format(strlen($output)) . " bytes</p>";
                
                if (strlen($output) === $testFile['size']) {
                    echo "<p class='success'>✅ Controller output size matches file size!</p>";
                } else {
                    echo "<p class='warning'>⚠️ Controller output size mismatch</p>";
                }
                
            } else {
                echo "<p class='error'>❌ Controller download failed - no output</p>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Controller test error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='/admin/storage?force_real=1'>🌐 Open Storage with Real Client</a></p>";
echo "<p><a href='/admin/storage'>📁 Open Storage (Auto-detect)</a></p>";
echo "<p><a href='/debug_real_nextcloud.php'>🔧 Debug Real Nextcloud</a></p>";

echo "</body></html>";
