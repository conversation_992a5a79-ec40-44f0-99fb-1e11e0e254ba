/**
 * StrixBudget API Client for Frontend
 * Handles authentication, token management, and API requests
 */
class StrixBudgetAPI {
    constructor(options = {}) {
        this.baseUrl = options.baseUrl || '/api/strixbudget';
        this.token = this.getStoredToken();
        this.tokenKey = 'strixbudget_api_token';
        this.userKey = 'strixbudget_user';
        this.expiryKey = 'strixbudget_token_expiry';
        
        // Auto-refresh token before expiry
        this.setupTokenRefresh();
    }

    /**
     * Login with email and password
     */
    async login(email, password, options = {}) {
        try {
            const response = await fetch(`${this.baseUrl}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    email: email,
                    password: password,
                    api_url: options.apiUrl,
                    save_credentials: options.saveCredentials || false
                })
            });

            const data = await response.json();

            if (data.success && data.data.token) {
                this.setToken(data.data.token, data.data.expires_in);
                this.setUser(data.data.user);
                return {
                    success: true,
                    token: data.data.token,
                    user: data.data.user,
                    message: data.message
                };
            } else {
                return {
                    success: false,
                    message: data.message || 'Грешка при влизане'
                };
            }
        } catch (error) {
            console.error('Login error:', error);
            return {
                success: false,
                message: 'Грешка при свързване: ' + error.message
            };
        }
    }

    /**
     * Set authentication token
     */
    setToken(token, expiresIn = 3600) {
        this.token = token;
        localStorage.setItem(this.tokenKey, token);
        
        // Set expiry time
        const expiryTime = Date.now() + (expiresIn * 1000);
        localStorage.setItem(this.expiryKey, expiryTime.toString());
    }

    /**
     * Get stored token
     */
    getStoredToken() {
        const token = localStorage.getItem(this.tokenKey);
        const expiry = localStorage.getItem(this.expiryKey);
        
        if (token && expiry) {
            const expiryTime = parseInt(expiry);
            if (Date.now() < expiryTime) {
                return token;
            } else {
                // Token expired, clear it
                this.clearToken();
            }
        }
        
        return null;
    }

    /**
     * Clear stored token and user data
     */
    clearToken() {
        this.token = null;
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.userKey);
        localStorage.removeItem(this.expiryKey);
    }

    /**
     * Set user data
     */
    setUser(user) {
        if (user) {
            localStorage.setItem(this.userKey, JSON.stringify(user));
        }
    }

    /**
     * Get stored user data
     */
    getUser() {
        const userData = localStorage.getItem(this.userKey);
        return userData ? JSON.parse(userData) : null;
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return !!this.getStoredToken();
    }

    /**
     * Setup automatic token refresh
     */
    setupTokenRefresh() {
        setInterval(() => {
            const expiry = localStorage.getItem(this.expiryKey);
            if (expiry) {
                const expiryTime = parseInt(expiry);
                const timeLeft = expiryTime - Date.now();
                
                // Refresh token if less than 5 minutes left
                if (timeLeft < 5 * 60 * 1000 && timeLeft > 0) {
                    this.refreshToken();
                }
            }
        }, 60000); // Check every minute
    }

    /**
     * Refresh authentication token
     */
    async refreshToken() {
        try {
            const response = await this.makeRequest('GET', '/auth/user');
            if (response.success) {
                // Token is still valid, extend expiry
                const expiryTime = Date.now() + (3600 * 1000); // 1 hour
                localStorage.setItem(this.expiryKey, expiryTime.toString());
            } else {
                // Token invalid, clear it
                this.clearToken();
            }
        } catch (error) {
            console.error('Token refresh error:', error);
            this.clearToken();
        }
    }

    /**
     * Make authenticated API request
     */
    async makeRequest(method, endpoint, data = null) {
        const url = `${this.baseUrl}${endpoint}`;
        
        const headers = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };

        // Add authorization header if token is available
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        const options = {
            method: method,
            headers: headers
        };

        // Add data for POST, PUT requests
        if (data && ['POST', 'PUT', 'PATCH'].includes(method)) {
            options.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(url, options);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            
            // Handle authentication errors
            if (!result.success && response.status === 401) {
                this.clearToken();
                throw new Error('Сесията е изтекла. Моля, влезте отново.');
            }
            
            return result;
        } catch (error) {
            console.error(`API request error (${method} ${url}):`, error);
            throw error;
        }
    }

    /**
     * Get current user information
     */
    async getCurrentUser() {
        return await this.makeRequest('GET', '/auth/user');
    }

    /**
     * Get bank accounts
     */
    async getBankAccounts() {
        return await this.makeRequest('GET', '/bank-accounts');
    }

    /**
     * Get transactions with optional filters
     */
    async getTransactions(filters = {}) {
        const queryString = new URLSearchParams(filters).toString();
        const endpoint = '/transactions' + (queryString ? '?' + queryString : '');
        return await this.makeRequest('GET', endpoint);
    }

    /**
     * Create new transaction
     */
    async createTransaction(transactionData) {
        return await this.makeRequest('POST', '/transactions', transactionData);
    }

    /**
     * Get counterparties
     */
    async getCounterparties(filters = {}) {
        const queryString = new URLSearchParams(filters).toString();
        const endpoint = '/counterparties' + (queryString ? '?' + queryString : '');
        return await this.makeRequest('GET', endpoint);
    }

    /**
     * Get user statistics
     */
    async getStatistics() {
        return await this.makeRequest('GET', '/statistics');
    }

    /**
     * Test API connection
     */
    async testConnection(credentials = {}) {
        return await this.makeRequest('POST', '/test-connection', credentials);
    }

    /**
     * Logout and clear stored data
     */
    logout() {
        this.clearToken();
        // Optionally make logout request to server
        // this.makeRequest('POST', '/auth/logout').catch(() => {});
    }
}

// Global instance
window.StrixBudgetAPI = StrixBudgetAPI;

// Usage examples:
/*
// 1. Initialize API client
const api = new StrixBudgetAPI();

// 2. Login and get token
const loginResult = await api.login('<EMAIL>', 'password');
if (loginResult.success) {
    console.log('Logged in successfully:', loginResult.user);
} else {
    console.error('Login failed:', loginResult.message);
}

// 3. Make authenticated requests
try {
    const accounts = await api.getBankAccounts();
    console.log('Bank accounts:', accounts.data);
    
    const transactions = await api.getTransactions({ limit: 10 });
    console.log('Recent transactions:', transactions.data);
    
    const stats = await api.getStatistics();
    console.log('Statistics:', stats.data);
} catch (error) {
    console.error('API error:', error.message);
}

// 4. Create new transaction
const newTransaction = await api.createTransaction({
    bank_account_id: 1,
    amount: 100.50,
    description: 'Test transaction',
    type: 'expense'
});

// 5. Check authentication status
if (api.isAuthenticated()) {
    console.log('User is authenticated');
    console.log('Current user:', api.getUser());
} else {
    console.log('User needs to login');
}
*/
