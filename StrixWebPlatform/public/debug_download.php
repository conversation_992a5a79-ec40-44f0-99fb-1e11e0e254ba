<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Services\MockNextcloudClient;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

echo "<!DOCTYPE html><html><head><title>Debug Download</title></head><body>";
echo "<h1>🔧 Debug Download Function</h1>";

// Test MockNextcloudClient download
echo "<h2>Testing MockNextcloudClient Download</h2>";

try {
    $mockClient = new MockNextcloudClient();
    
    echo "<h3>Test 1: Download existing file</h3>";
    $content = $mockClient->downloadFile('/test.txt');
    if ($content !== false) {
        echo "<p style='color: green;'>✅ Download successful</p>";
        echo "<p><strong>Content:</strong> " . htmlspecialchars($content) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Download failed</p>";
    }
    
    echo "<h3>Test 2: List directory to see available files</h3>";
    $files = $mockClient->listDirectory('/');
    echo "<pre>" . print_r($files, true) . "</pre>";
    
    if (!empty($files)) {
        $firstFile = null;
        foreach ($files as $file) {
            if (!$file['is_directory']) {
                $firstFile = $file;
                break;
            }
        }
        
        if ($firstFile) {
            echo "<h3>Test 3: Download first available file: " . htmlspecialchars($firstFile['name']) . "</h3>";
            $content = $mockClient->downloadFile($firstFile['path']);
            if ($content !== false) {
                echo "<p style='color: green;'>✅ Download successful</p>";
                echo "<p><strong>Content length:</strong> " . strlen($content) . " bytes</p>";
                echo "<p><strong>Content preview:</strong> " . htmlspecialchars(substr($content, 0, 100)) . "...</p>";
            } else {
                echo "<p style='color: red;'>❌ Download failed</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ No files found to test download</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test actual download URL
echo "<h2>Testing Download URLs</h2>";

$testPaths = ['/test.txt', '/demo.pdf', '/sample.docx'];

foreach ($testPaths as $path) {
    $downloadUrl = '/admin/storage/download?path=' . urlencode($path);
    echo "<p><a href='" . $downloadUrl . "' target='_blank'>Test download: " . htmlspecialchars($path) . "</a></p>";
}

// Test permissions
echo "<h2>Testing Permissions</h2>";
echo "<p>storage.download permission: " . ($app->hasPermission('storage.download') ? '✅ GRANTED' : '❌ DENIED') . "</p>";

// Test current user
$currentUser = $app->getCurrentUser();
echo "<h2>Current User</h2>";
echo "<p>User ID: " . $currentUser['id'] . "</p>";
echo "<p>Username: " . htmlspecialchars($currentUser['username']) . "</p>";
echo "<p>Is Admin: " . ($currentUser['is_admin'] ? 'Yes' : 'No') . "</p>";

echo "<h2>Direct Download Test</h2>";
echo "<form method='GET' action='/admin/storage/download'>";
echo "<input type='text' name='path' value='/test.txt' placeholder='File path'>";
echo "<button type='submit'>Test Download</button>";
echo "</form>";

echo "<p><a href='/admin/storage'>← Back to Storage</a></p>";

echo "</body></html>";
?>
