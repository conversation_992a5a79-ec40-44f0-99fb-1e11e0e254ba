<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Core\Router;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

echo "<!DOCTYPE html><html><head><title>Debug Routes</title></head><body>";
echo "<h1>🔧 Debug Routes</h1>";

echo "<h2>Current Request Info</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><td><strong>REQUEST_METHOD:</strong></td><td>" . $_SERVER['REQUEST_METHOD'] . "</td></tr>";
echo "<tr><td><strong>REQUEST_URI:</strong></td><td>" . $_SERVER['REQUEST_URI'] . "</td></tr>";
echo "<tr><td><strong>SCRIPT_NAME:</strong></td><td>" . $_SERVER['SCRIPT_NAME'] . "</td></tr>";
echo "<tr><td><strong>PATH_INFO:</strong></td><td>" . ($_SERVER['PATH_INFO'] ?? 'Not set') . "</td></tr>";
echo "<tr><td><strong>QUERY_STRING:</strong></td><td>" . ($_SERVER['QUERY_STRING'] ?? 'Not set') . "</td></tr>";
echo "</table>";

echo "<h2>Test Route Simulation</h2>";

// Test the router directly
try {
    echo "<h3>1. Testing Router Creation</h3>";
    $router = new Router();
    echo "<p style='color: green;'>✅ Router created successfully</p>";
    
    echo "<h3>2. Adding Routes</h3>";
    $router->middleware('AdminMiddleware')->get('/admin/profile/nextcloud-settings', 'UserSettingsController@nextcloudSettings');
    $router->middleware('AdminMiddleware')->post('/admin/profile/nextcloud-settings/update', 'UserSettingsController@updateNextcloudSettings');
    $router->middleware('AdminMiddleware')->post('/admin/profile/nextcloud-settings/test', 'UserSettingsController@testNextcloudConnection');
    echo "<p style='color: green;'>✅ Routes added successfully</p>";
    
    echo "<h3>3. Testing Route Matching</h3>";
    
    // Test different URIs
    $testUris = [
        '/admin/profile/nextcloud-settings',
        '/admin/profile/nextcloud-settings/update',
        '/admin/profile/nextcloud-settings/test',
        '/admin/profile/nextcloud-settings/delete'
    ];
    
    foreach ($testUris as $uri) {
        echo "<h4>Testing URI: $uri</h4>";
        
        // Simulate the route matching logic
        $pattern = '#^' . preg_replace('/\{([^}]+)\}/', '([^/]+)', $uri) . '$#';
        echo "<p>Pattern: $pattern</p>";
        
        if (preg_match($pattern, $uri)) {
            echo "<p style='color: green;'>✅ Route would match</p>";
        } else {
            echo "<p style='color: red;'>❌ Route would not match</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Router error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Test Direct Controller Call</h2>";

try {
    echo "<h3>Testing UserSettingsController</h3>";
    
    // Set up environment for controller
    $_POST['_token'] = $_SESSION['csrf_token'];
    $_POST['server_url'] = 'https://demo.nextcloud.com';
    $_POST['username'] = 'testuser';
    $_POST['password'] = 'testpass';
    
    $controller = new \Strix\ERP\Controllers\UserSettingsController();
    echo "<p style='color: green;'>✅ Controller created successfully</p>";
    
    // Test if methods exist
    $methods = ['nextcloudSettings', 'updateNextcloudSettings', 'testNextcloudConnection'];
    foreach ($methods as $method) {
        if (method_exists($controller, $method)) {
            echo "<p style='color: green;'>✅ Method $method exists</p>";
        } else {
            echo "<p style='color: red;'>❌ Method $method does not exist</p>";
        }
    }
    
    echo "<h4>Testing updateNextcloudSettings method</h4>";
    
    // Capture output
    ob_start();
    try {
        $controller->updateNextcloudSettings();
        $output = ob_get_clean();
        echo "<p style='color: green;'>✅ Method executed successfully</p>";
        echo "<p><strong>Output:</strong> " . htmlspecialchars($output) . "</p>";
        echo "<p><strong>Output length:</strong> " . strlen($output) . " characters</p>";
        
        // Try to parse as JSON
        if (!empty($output)) {
            $decoded = json_decode($output, true);
            if ($decoded !== null) {
                echo "<p style='color: green;'>✅ Output is valid JSON</p>";
                echo "<pre>" . print_r($decoded, true) . "</pre>";
            } else {
                echo "<p style='color: red;'>❌ Output is not valid JSON</p>";
                echo "<p>JSON error: " . json_last_error_msg() . "</p>";
            }
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>❌ Method execution failed: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Controller error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Test Manual HTTP Request</h2>";
echo "<button onclick='testManualRequest()'>Test Manual Request</button>";
echo "<div id='manualResult'></div>";

echo "<script>
function testManualRequest() {
    const result = document.getElementById('manualResult');
    result.innerHTML = '<p style=\"color: blue;\">Testing manual request...</p>';
    
    const formData = new FormData();
    formData.append('_token', '" . $_SESSION['csrf_token'] . "');
    formData.append('server_url', 'https://demo.nextcloud.com');
    formData.append('username', 'testuser');
    formData.append('password', 'testpass');
    
    fetch('/admin/profile/nextcloud-settings/update', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('Manual response:', response);
        console.log('Status:', response.status);
        console.log('Headers:', [...response.headers.entries()]);
        
        return response.text();
    })
    .then(text => {
        console.log('Manual response text:', text);
        result.innerHTML = '<h4>Response:</h4><pre>' + text.substring(0, 1000) + '</pre>';
    })
    .catch(error => {
        console.error('Manual error:', error);
        result.innerHTML = '<p style=\"color: red;\">Error: ' + error.message + '</p>';
    });
}
</script>";

echo "</body></html>";
?>
