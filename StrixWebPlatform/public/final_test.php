<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\User;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

echo "<h1>Final Test - Menu Permissions</h1>";

echo "<h2>1. Session Status:</h2>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>Is Logged In: " . ($app->isLoggedIn() ? 'Yes' : 'No') . "</p>";

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

$currentUser = $app->getCurrentUser();
echo "<h2>2. Current User:</h2>";
echo "<p>Username: " . $currentUser['username'] . "</p>";
echo "<p>Full Name: " . $currentUser['full_name'] . "</p>";
echo "<p>Is Admin: " . ($currentUser['is_admin'] ? 'Yes' : 'No') . "</p>";

echo "<h2>3. Permissions in Session:</h2>";
$sessionPermissions = $currentUser['permissions'] ?? [];
echo "<p>Total permissions: " . count($sessionPermissions) . "</p>";

$taskPermissions = array_filter($sessionPermissions, fn($p) => strpos($p, 'tasks.') === 0);
echo "<p>Task permissions in session: " . count($taskPermissions) . "</p>";
foreach ($taskPermissions as $perm) {
    echo "<li>$perm</li>";
}

echo "<h2>4. Permission Checks:</h2>";
$checks = [
    'tasks.view' => $app->hasPermission('tasks.view'),
    'users.view' => $app->hasPermission('users.view'),
    'groups.view' => $app->hasPermission('groups.view'),
    'admin.dashboard' => $app->hasPermission('admin.dashboard'),
];

foreach ($checks as $permission => $hasIt) {
    echo "<p><strong>$permission:</strong> " . ($hasIt ? '✅ Yes' : '❌ No') . "</p>";
}

echo "<h2>5. Fresh Database Check:</h2>";
$admin = User::findByUsername($currentUser['username']);
if ($admin) {
    $freshPermissions = $admin->getPermissionNames();
    $freshTaskPermissions = array_filter($freshPermissions, fn($p) => strpos($p, 'tasks.') === 0);
    
    echo "<p>Fresh permissions from DB: " . count($freshPermissions) . "</p>";
    echo "<p>Fresh task permissions: " . count($freshTaskPermissions) . "</p>";
    
    $sessionHasTasksView = in_array('tasks.view', $sessionPermissions);
    $dbHasTasksView = in_array('tasks.view', $freshPermissions);
    
    echo "<p>Session has tasks.view: " . ($sessionHasTasksView ? 'Yes' : 'No') . "</p>";
    echo "<p>DB has tasks.view: " . ($dbHasTasksView ? 'Yes' : 'No') . "</p>";
    
    if (!$sessionHasTasksView && $dbHasTasksView) {
        echo "<p style='color: orange;'>⚠️ Session is outdated! Need to refresh.</p>";
        echo "<p><a href='/force_refresh_session.php'>Force Refresh Session</a></p>";
    }
}

echo "<h2>6. Menu Logic Test:</h2>";
echo "<p>The sidebar.php file checks: \$app->hasPermission('tasks.view')</p>";
echo "<p>This should return: " . ($app->hasPermission('tasks.view') ? 'TRUE (show menu)' : 'FALSE (hide menu)') . "</p>";

if ($app->hasPermission('tasks.view')) {
    echo "<p style='color: green;'>✅ Tasks menu SHOULD be visible</p>";
} else {
    echo "<p style='color: red;'>❌ Tasks menu will be HIDDEN</p>";
}

echo "<h2>7. Actions:</h2>";
echo "<p><a href='/admin'>Go to Admin Dashboard</a></p>";
echo "<p><a href='/admin/tasks'>Try Tasks Page</a></p>";
echo "<p><a href='/logout_and_login.php'>Logout and Login Again</a></p>";
