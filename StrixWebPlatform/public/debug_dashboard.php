<?php
/**
 * Debug script for StrixBudget dashboard issues
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$envFile = __DIR__ . '/../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        [$name, $value] = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

use Strix\ERP\Services\StrixBudgetClient;
use Strix\ERP\Models\StrixBudget\BaseStrixBudgetModel;
use Strix\ERP\Models\StrixBudget\Transaction;
use Strix\ERP\Models\StrixBudget\BankAccount;

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>🐛 Dashboard Debug</h1>
        <p class="text-muted">Debugging StrixBudget dashboard issues</p>

        <?php
        echo "<h2>1. Environment Check</h2>";
        echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
        echo "<p><strong>Error Reporting:</strong> " . error_reporting() . "</p>";
        echo "<p><strong>Display Errors:</strong> " . ini_get('display_errors') . "</p>";

        echo "<h2>2. Client Initialization</h2>";
        try {
            $apiUrl = $_ENV['STRIXBUDGET_API_URL'] ?? 'http://localhost:8000/api';
            $client = new StrixBudgetClient($apiUrl);
            echo "<p class='success'>✅ Client created: $apiUrl</p>";
            echo "<p><strong>Mock Mode:</strong> " . ($client->isMockMode() ? 'Yes' : 'No') . "</p>";
            
            BaseStrixBudgetModel::setClient($client);
            echo "<p class='success'>✅ Client set for models</p>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Client error: " . $e->getMessage() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
            exit;
        }

        echo "<h2>3. Step-by-step Dashboard Stats</h2>";
        
        $stats = [
            'total_accounts' => 0,
            'total_balance' => 0,
            'recent_transactions' => [],
            'recent_transfers' => [],
            'account_balances' => [],
            'monthly_summary' => []
        ];

        // Step 1: User Statistics
        echo "<h3>Step 1: User Statistics</h3>";
        try {
            $userStats = $client->getUserStatistics();
            echo "<p class='success'>✅ User stats retrieved</p>";
            echo "<pre>" . json_encode($userStats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
            
            if ($userStats['success'] && isset($userStats['data'])) {
                $stats = array_merge($stats, $userStats['data']);
            }
        } catch (Exception $e) {
            echo "<p class='error'>❌ User stats error: " . $e->getMessage() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }

        // Step 2: Bank Accounts
        echo "<h3>Step 2: Bank Accounts</h3>";
        try {
            echo "<p class='info'>Calling BankAccount::getActive()...</p>";
            $accounts = BankAccount::getActive();
            echo "<p class='success'>✅ Bank accounts retrieved: " . count($accounts) . "</p>";
            
            $stats['total_accounts'] = count($accounts);
            $stats['account_balances'] = $accounts;

            $totalBalance = 0;
            foreach ($accounts as $account) {
                $balance = (float) ($account->balance ?? 0);
                $totalBalance += $balance;
                echo "<p>Account: " . ($account->name ?? 'Unknown') . " - Balance: $balance</p>";
            }
            $stats['total_balance'] = $totalBalance;
            echo "<p><strong>Total Balance:</strong> $totalBalance</p>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Bank accounts error: " . $e->getMessage() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }

        // Step 3: Recent Transactions (the problematic one)
        echo "<h3>Step 3: Recent Transactions</h3>";
        try {
            echo "<p class='info'>Calling Transaction::getFiltered(['per_page' => 10])...</p>";
            
            // First, let's test the raw API call
            echo "<h4>3a. Raw API Call</h4>";
            $rawResponse = $client->getTransactions(['per_page' => 10]);
            echo "<p class='success'>✅ Raw API response received</p>";
            echo "<p><strong>Success:</strong> " . ($rawResponse['success'] ? 'Yes' : 'No') . "</p>";
            echo "<p><strong>Data type:</strong> " . gettype($rawResponse['data'] ?? null) . "</p>";
            
            if (isset($rawResponse['data']) && is_array($rawResponse['data'])) {
                echo "<p><strong>Data count:</strong> " . count($rawResponse['data']) . "</p>";
                
                if (!empty($rawResponse['data'])) {
                    $firstItem = $rawResponse['data'][0];
                    echo "<p><strong>First item type:</strong> " . gettype($firstItem) . "</p>";
                    echo "<p><strong>First item:</strong></p>";
                    echo "<pre>" . json_encode($firstItem, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                }
            }
            
            // Now test the model method
            echo "<h4>3b. Model Method Call</h4>";
            $recentTransactions = Transaction::getFiltered(['per_page' => 10]);
            echo "<p class='success'>✅ Transactions retrieved: " . count($recentTransactions) . "</p>";
            
            $stats['recent_transactions'] = array_slice($recentTransactions, 0, 10);
            
            if (!empty($recentTransactions)) {
                $firstTransaction = $recentTransactions[0];
                echo "<p><strong>First transaction type:</strong> " . get_class($firstTransaction) . "</p>";
                echo "<p><strong>First transaction attributes:</strong></p>";
                echo "<pre>" . json_encode($firstTransaction->getAttributes(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Transactions error: " . $e->getMessage() . "</p>";
            echo "<p><strong>Error file:</strong> " . $e->getFile() . "</p>";
            echo "<p><strong>Error line:</strong> " . $e->getLine() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }

        echo "<h2>4. Final Stats</h2>";
        echo "<pre>" . json_encode($stats, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
        ?>

        <div class="mt-5">
            <a href="/admin/strixbudget" class="btn btn-primary">🏠 Try Dashboard Again</a>
            <a href="/test_strixbudget_models.php" class="btn btn-secondary">🧪 Model Tests</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
