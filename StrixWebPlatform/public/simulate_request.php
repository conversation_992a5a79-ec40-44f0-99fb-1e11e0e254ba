<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Core\Router;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

echo "<!DOCTYPE html><html><head><title>Simulate Request</title></head><body>";
echo "<h1>🧪 Simulate Request</h1>";

// Simulate the exact same request that would come from the form
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['REQUEST_URI'] = '/admin/profile/nextcloud-settings/update';

$_POST = [
    '_token' => $_SESSION['csrf_token'],
    'server_url' => 'https://demo.nextcloud.com',
    'username' => 'testuser',
    'password' => 'testpass',
    'verify_ssl' => '1',
    'timeout' => '30',
    'default_folder' => '/ERP_Test',
    'auto_create_folders' => '1'
];

echo "<h2>Request Simulation</h2>";
echo "<p><strong>Method:</strong> " . $_SERVER['REQUEST_METHOD'] . "</p>";
echo "<p><strong>URI:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p><strong>POST Data:</strong></p>";
echo "<pre>" . print_r($_POST, true) . "</pre>";

echo "<h2>Router Execution</h2>";

try {
    // Create router and add routes exactly like in index.php
    $router = new Router();
    
    // Add all the routes
    $router->middleware('AdminMiddleware')->get('/admin', 'AdminController@dashboard');
    $router->middleware('AdminMiddleware')->get('/admin/users', 'UserController@index');
    $router->middleware('AdminMiddleware')->post('/admin/users', 'UserController@store');
    $router->middleware('AdminMiddleware')->get('/admin/users/{id}', 'UserController@show');
    $router->middleware('AdminMiddleware')->put('/admin/users/{id}', 'UserController@update');
    $router->middleware('AdminMiddleware')->delete('/admin/users/{id}', 'UserController@destroy');
    
    $router->middleware('AdminMiddleware')->get('/admin/groups', 'GroupController@index');
    $router->middleware('AdminMiddleware')->post('/admin/groups', 'GroupController@store');
    $router->middleware('AdminMiddleware')->get('/admin/groups/{id}', 'GroupController@show');
    $router->middleware('AdminMiddleware')->put('/admin/groups/{id}', 'GroupController@update');
    $router->middleware('AdminMiddleware')->delete('/admin/groups/{id}', 'GroupController@destroy');
    
    $router->middleware('AdminMiddleware')->get('/admin/tasks', 'TaskController@index');
    $router->middleware('AdminMiddleware')->post('/admin/tasks', 'TaskController@store');
    $router->middleware('AdminMiddleware')->get('/admin/tasks/{id}', 'TaskController@show');
    $router->middleware('AdminMiddleware')->put('/admin/tasks/{id}', 'TaskController@update');
    $router->middleware('AdminMiddleware')->delete('/admin/tasks/{id}', 'TaskController@destroy');
    $router->middleware('AdminMiddleware')->post('/admin/tasks/{id}/assign', 'TaskController@assign');
    $router->middleware('AdminMiddleware')->post('/admin/tasks/{id}/complete', 'TaskController@complete');
    
    // Storage routes
    $router->middleware('AdminMiddleware')->get('/admin/storage', 'StorageController@index');
    $router->middleware('AdminMiddleware')->post('/admin/storage/upload', 'StorageController@upload');
    $router->middleware('AdminMiddleware')->get('/admin/storage/download', 'StorageController@download');
    $router->middleware('AdminMiddleware')->post('/admin/storage/create-folder', 'StorageController@createFolder');
    $router->middleware('AdminMiddleware')->post('/admin/storage/delete', 'StorageController@delete');
    $router->middleware('AdminMiddleware')->post('/admin/storage/rename', 'StorageController@rename');
    
    // User settings routes
    $router->middleware('AdminMiddleware')->get('/admin/profile', 'UserSettingsController@profile');
    $router->middleware('AdminMiddleware')->post('/admin/profile/update', 'UserSettingsController@updateProfile');
    $router->middleware('AdminMiddleware')->get('/admin/profile/nextcloud-settings', 'UserSettingsController@nextcloudSettings');
    $router->middleware('AdminMiddleware')->post('/admin/profile/nextcloud-settings/update', 'UserSettingsController@updateNextcloudSettings');
    $router->middleware('AdminMiddleware')->post('/admin/profile/nextcloud-settings/test', 'UserSettingsController@testNextcloudConnection');
    $router->middleware('AdminMiddleware')->post('/admin/profile/nextcloud-settings/delete', 'UserSettingsController@deleteNextcloudSettings');
    $router->middleware('AdminMiddleware')->get('/admin/profile/nextcloud-settings/get', 'UserSettingsController@getNextcloudSettings');
    
    echo "<p style='color: green;'>✅ Router created and routes added</p>";
    
    echo "<h3>Dispatching Request</h3>";
    
    // Capture all output
    ob_start();
    
    try {
        $router->dispatch($_SERVER['REQUEST_METHOD'], $_SERVER['REQUEST_URI']);
        $output = ob_get_clean();
        
        echo "<p style='color: green;'>✅ Request dispatched successfully</p>";
        echo "<p><strong>Output length:</strong> " . strlen($output) . " characters</p>";
        
        if (!empty($output)) {
            echo "<h4>Output:</h4>";
            echo "<pre>" . htmlspecialchars($output) . "</pre>";
            
            // Try to parse as JSON
            $decoded = json_decode($output, true);
            if ($decoded !== null) {
                echo "<p style='color: green;'>✅ Output is valid JSON</p>";
                echo "<h4>Parsed JSON:</h4>";
                echo "<pre>" . print_r($decoded, true) . "</pre>";
            } else {
                echo "<p style='color: red;'>❌ Output is not valid JSON</p>";
                echo "<p>JSON error: " . json_last_error_msg() . "</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ No output generated</p>";
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>❌ Exception during dispatch: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error setting up router: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</body></html>";
?>
