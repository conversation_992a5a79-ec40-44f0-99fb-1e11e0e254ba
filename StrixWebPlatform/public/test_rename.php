<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';

echo "<!DOCTYPE html><html><head><title>Test Rename</title></head><body>";
echo "<h1>📝 Test Rename</h1>";

// Handle POST request for rename
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Simulate rename request
        $_POST['old_path'] = '/Photos/4K_B.jpg';
        $_POST['new_name'] = '4K_Renamed_Test.jpg';

        // Generate CSRF token
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        $_POST['_token'] = $_SESSION['csrf_token'];
        
        echo "<p><strong>Testing rename:</strong> " . htmlspecialchars($_POST['old_path']) . " → " . htmlspecialchars($_POST['new_name']) . "</p>";
        
        $controller = new \Strix\ERP\Controllers\StorageController();
        
        // Capture output
        ob_start();
        $controller->rename();
        $output = ob_get_contents();
        ob_end_clean();
        
        echo "<p><strong>Response:</strong></p>";
        echo "<pre>" . htmlspecialchars($output) . "</pre>";
        
        // Try to decode JSON response
        $response = json_decode($output, true);
        if ($response) {
            if (isset($response['success']) && $response['success']) {
                echo "<p style='color: green;'>✅ Rename successful: " . htmlspecialchars($response['message']) . "</p>";
            } else {
                echo "<p style='color: red;'>❌ Rename failed: " . htmlspecialchars($response['error'] ?? 'Unknown error') . "</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
} else {
    // Show form
    echo "<form method='POST'>";
    echo "<p>Click the button to test renaming 4K_B.jpg to 4K_Renamed_Test.jpg</p>";
    echo "<button type='submit'>Test Rename</button>";
    echo "</form>";
}

echo "<hr>";
echo "<p><a href='/admin/storage'>← Back to Storage</a></p>";
echo "<p><a href='/test_storage_operations.php'>🧪 Run Storage Tests</a></p>";

echo "</body></html>";
