<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\UserNextcloudSettings;
use Strix\ERP\Services\NextcloudClient;
use Strix\ERP\Services\MockNextcloudClient;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'admin',
    'role' => 'admin',
    'permissions' => ['storage.view', 'storage.download', 'storage.edit']
];

echo "<!DOCTYPE html><html><head><title>Compare Clients</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .section { border: 1px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .comparison { display: flex; gap: 20px; }
    .client-test { flex: 1; border: 1px solid #ccc; padding: 15px; border-radius: 5px; }
    .file-list { margin: 10px 0; }
    .file-item { padding: 5px; margin: 2px 0; border-radius: 3px; display: flex; align-items: center; }
    .file-item.directory { background: #e8f4fd; }
    .file-item.file { background: #f8f9fa; }
    .file-icon { margin-right: 8px; }
    .stats { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; }
</style></head><body>";

echo "<h1>🔄 Compare Mock vs Real Client</h1>";

echo "<div class='comparison'>";

// Test Mock Client
echo "<div class='client-test'>";
echo "<h2>📋 Mock Client</h2>";

try {
    $mockClient = new MockNextcloudClient();
    echo "<p class='success'>✅ MockNextcloudClient created</p>";
    
    $mockFiles = $mockClient->listDirectory('/');
    echo "<p><strong>Total items:</strong> " . count($mockFiles) . "</p>";
    
    $mockDirs = array_filter($mockFiles, fn($item) => $item['is_directory']);
    $mockRegularFiles = array_filter($mockFiles, fn($item) => !$item['is_directory']);
    
    echo "<div class='stats'>";
    echo "<p><strong>Directories:</strong> " . count($mockDirs) . "</p>";
    echo "<p><strong>Files:</strong> " . count($mockRegularFiles) . "</p>";
    echo "</div>";
    
    echo "<div class='file-list'>";
    foreach ($mockFiles as $item) {
        $icon = $item['is_directory'] ? '📁' : '📄';
        $type = $item['is_directory'] ? 'DIR' : 'FILE';
        echo "<div class='file-item " . ($item['is_directory'] ? 'directory' : 'file') . "'>";
        echo "<span class='file-icon'>$icon</span>";
        echo "<strong>" . htmlspecialchars($item['name']) . "</strong> ($type)";
        echo "</div>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Mock client error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Test Real Client
echo "<div class='client-test'>";
echo "<h2>🌐 Real Client</h2>";

try {
    $userSettings = UserNextcloudSettings::getActiveForUser(1);
    
    if (!$userSettings) {
        echo "<p class='error'>❌ No user settings found</p>";
    } else {
        $realClient = new NextcloudClient($userSettings);
        echo "<p class='success'>✅ NextcloudClient created</p>";
        
        $realFiles = $realClient->listDirectory('/');
        echo "<p><strong>Total items:</strong> " . count($realFiles) . "</p>";
        
        $realDirs = array_filter($realFiles, fn($item) => $item['is_directory']);
        $realRegularFiles = array_filter($realFiles, fn($item) => !$item['is_directory']);
        
        echo "<div class='stats'>";
        echo "<p><strong>Directories:</strong> " . count($realDirs) . "</p>";
        echo "<p><strong>Files:</strong> " . count($realRegularFiles) . "</p>";
        echo "</div>";
        
        if (!empty($realFiles)) {
            echo "<div class='file-list'>";
            foreach ($realFiles as $item) {
                $icon = $item['is_directory'] ? '📁' : '📄';
                $type = $item['is_directory'] ? 'DIR' : 'FILE';
                $size = $item['is_directory'] ? '' : ' (' . number_format($item['size']) . ' bytes)';
                echo "<div class='file-item " . ($item['is_directory'] ? 'directory' : 'file') . "'>";
                echo "<span class='file-icon'>$icon</span>";
                echo "<strong>" . htmlspecialchars($item['name']) . "</strong> ($type)$size";
                echo "</div>";
            }
            echo "</div>";
        } else {
            echo "<p class='warning'>⚠️ No items found</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Real client error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

echo "</div>";

// Comparison summary
echo "<div class='section'>";
echo "<h2>📊 Comparison Summary</h2>";

if (isset($mockFiles) && isset($realFiles)) {
    echo "<table style='width: 100%; border-collapse: collapse;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Metric</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Mock Client</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Real Client</th>";
    echo "<th style='border: 1px solid #ddd; padding: 8px;'>Status</th>";
    echo "</tr>";
    
    $comparisons = [
        'Total Items' => [count($mockFiles), count($realFiles)],
        'Directories' => [count($mockDirs), count($realDirs)],
        'Files' => [count($mockRegularFiles), count($realRegularFiles)]
    ];
    
    foreach ($comparisons as $metric => $values) {
        $status = $values[1] > 0 ? '✅' : '❌';
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>$metric</strong></td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: center;'>" . $values[0] . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: center;'>" . $values[1] . "</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: center;'>$status</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    if (count($realDirs) === 0 && count($realRegularFiles) > 0) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<p class='warning'><strong>⚠️ Issue Detected:</strong></p>";
        echo "<p>Real client shows files but no directories. This suggests the directory parsing logic needs fixing.</p>";
        echo "</div>";
    } elseif (count($realFiles) === 0) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<p class='error'><strong>❌ Problem Detected:</strong></p>";
        echo "<p>Real client shows no items at all. Check connection and authentication.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<p class='success'><strong>✅ Looking Good:</strong></p>";
        echo "<p>Real client is showing both files and directories correctly.</p>";
        echo "</div>";
    }
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>Quick Actions</h2>";
echo "<a href='/test_directory_listing.php' style='padding: 10px 15px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 3px;'>🧪 Detailed Directory Test</a>";
echo "<a href='/admin/storage' style='padding: 10px 15px; margin: 5px; background: #28a745; color: white; text-decoration: none; border-radius: 3px;'>📁 Open Storage (Auto)</a>";
echo "<a href='/admin/storage?force_real=1' style='padding: 10px 15px; margin: 5px; background: #dc3545; color: white; text-decoration: none; border-radius: 3px;'>🌐 Force Real Client</a>";
echo "</div>";

echo "</body></html>";
