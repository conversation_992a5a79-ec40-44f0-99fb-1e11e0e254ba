<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\User;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

echo "<h1>Force Refresh Session</h1>";

try {
    // Find admin user
    $admin = User::findByUsername('admin');
    if (!$admin) {
        echo "<p style='color: red;'>Admin user not found!</p>";
        exit;
    }
    
    echo "<h2>Admin User Found:</h2>";
    echo "<p>ID: {$admin->id}</p>";
    echo "<p>Username: {$admin->username}</p>";
    echo "<p>Full Name: {$admin->getFullName()}</p>";
    
    // Get fresh permissions from database
    $permissions = $admin->getPermissionNames();
    echo "<h2>Fresh Permissions from DB:</h2>";
    echo "<p>Total: " . count($permissions) . "</p>";
    
    $taskPermissions = array_filter($permissions, fn($p) => strpos($p, 'tasks.') === 0);
    echo "<p>Task permissions: " . count($taskPermissions) . "</p>";
    foreach ($taskPermissions as $perm) {
        echo "<li>$perm</li>";
    }
    
    // Check if user is in administrators group
    $isAdmin = $admin->hasGroup('administrators');
    echo "<p>Is in administrators group: " . ($isAdmin ? 'Yes' : 'No') . "</p>";
    
    // Force update session
    echo "<h2>🔄 Force updating session...</h2>";
    
    $sessionData = [
        'id' => $admin->id,
        'username' => $admin->username,
        'email' => $admin->email,
        'first_name' => $admin->first_name,
        'last_name' => $admin->last_name,
        'full_name' => $admin->getFullName(),
        'permissions' => $permissions,
        'is_admin' => $isAdmin
    ];
    
    // Clear existing session data
    unset($_SESSION['user']);
    
    // Set new session data
    $app->setCurrentUser($sessionData);
    
    echo "<p>✅ Session updated!</p>";
    
    // Verify the update
    $updatedUser = $app->getCurrentUser();
    echo "<h2>Verification:</h2>";
    echo "<p>Session has user: " . (isset($_SESSION['user']) ? 'Yes' : 'No') . "</p>";
    echo "<p>App isLoggedIn: " . ($app->isLoggedIn() ? 'Yes' : 'No') . "</p>";
    echo "<p>Permissions in session: " . count($updatedUser['permissions'] ?? []) . "</p>";
    echo "<p>Has tasks.view: " . ($app->hasPermission('tasks.view') ? 'Yes' : 'No') . "</p>";
    
    echo "<h2>Actions:</h2>";
    echo "<p><a href='/admin'>Go to Admin Dashboard</a></p>";
    echo "<p><a href='/admin/tasks'>Go to Tasks</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
