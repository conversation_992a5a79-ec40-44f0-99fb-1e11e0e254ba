<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Controllers\StorageController;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';

echo "<!DOCTYPE html><html><head><title>Test Storage Operations</title></head><body>";
echo "<h1>🧪 Test Storage Operations</h1>";

// Test 1: List files in Photos directory
echo "<h2>Test 1: List Files in /Photos</h2>";
try {
    $controller = new StorageController();
    
    // Use reflection to get the client
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('getNextcloudClient');
    $method->setAccessible(true);
    $client = $method->invoke($controller);
    
    echo "<p><strong>Client type:</strong> " . get_class($client) . "</p>";
    
    $files = $client->listDirectory('/Photos');
    echo "<p><strong>Files found:</strong> " . count($files) . "</p>";
    echo "<ul>";
    foreach ($files as $file) {
        echo "<li>" . htmlspecialchars($file['name']) . " (" . htmlspecialchars($file['path']) . ")</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 2: Check if 4K_B.jpg exists
echo "<h2>Test 2: Check if 4K_B.jpg exists</h2>";
try {
    $exists = $client->exists('/Photos/4K_B.jpg');
    echo "<p style='color: " . ($exists ? 'green' : 'red') . ";'>";
    echo $exists ? "✅ File exists" : "❌ File does not exist";
    echo "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 3: Test download URL
echo "<h2>Test 3: Test Download URL</h2>";
try {
    $downloadUrl = $client->getDownloadUrl('/Photos/4K_B.jpg');
    echo "<p><strong>Download URL:</strong> <a href='" . htmlspecialchars($downloadUrl) . "' target='_blank'>" . htmlspecialchars($downloadUrl) . "</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 4: Test download content
echo "<h2>Test 4: Test Download Content</h2>";
try {
    $content = $client->downloadFile('/Photos/4K_B.jpg');
    if ($content !== false) {
        echo "<p style='color: green;'>✅ Download successful</p>";
        echo "<p><strong>Content length:</strong> " . strlen($content) . " bytes</p>";
        echo "<p><strong>Content preview:</strong> " . htmlspecialchars(substr($content, 0, 100)) . "...</p>";
    } else {
        echo "<p style='color: red;'>❌ Download failed</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test 5: Test rename operation
echo "<h2>Test 5: Test Rename Operation</h2>";
try {
    echo "<p>Renaming 4K_B.jpg to 4K_Test.jpg...</p>";
    $result = $client->move('/Photos/4K_B.jpg', '/Photos/4K_Test.jpg');
    
    if ($result) {
        echo "<p style='color: green;'>✅ Rename successful</p>";
        
        // Check if old file is gone and new file exists
        $oldExists = $client->exists('/Photos/4K_B.jpg');
        $newExists = $client->exists('/Photos/4K_Test.jpg');
        
        echo "<p>Old file exists: " . ($oldExists ? "YES" : "NO") . "</p>";
        echo "<p>New file exists: " . ($newExists ? "YES" : "NO") . "</p>";
        
        // List files again
        echo "<p><strong>Files after rename:</strong></p>";
        $files = $client->listDirectory('/Photos');
        echo "<ul>";
        foreach ($files as $file) {
            echo "<li>" . htmlspecialchars($file['name']) . " (" . htmlspecialchars($file['path']) . ")</li>";
        }
        echo "</ul>";
        
    } else {
        echo "<p style='color: red;'>❌ Rename failed</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</body></html>";
