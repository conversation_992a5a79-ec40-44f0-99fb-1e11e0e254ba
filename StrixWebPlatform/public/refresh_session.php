<?php

/**
 * Refresh user session with updated permissions
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\User;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

header('Content-Type: text/html; charset=UTF-8');

try {
    echo "<h1>🔄 Refresh Session</h1>";
    
    // Initialize application
    $app = Application::getInstance();
    
    if ($app->isLoggedIn()) {
        $currentUser = $app->getCurrentUser();
        echo "<p>Current user: " . $currentUser['full_name'] . "</p>";
        
        // Reload user from database with fresh permissions
        $user = User::find($currentUser['id']);
        if ($user) {
            $permissions = $user->getPermissionNames();
            
            echo "<h2>Old permissions (" . count($currentUser['permissions']) . "):</h2>";
            echo "<ul>";
            foreach ($currentUser['permissions'] as $permission) {
                echo "<li>$permission</li>";
            }
            echo "</ul>";
            
            echo "<h2>New permissions (" . count($permissions) . "):</h2>";
            echo "<ul>";
            foreach ($permissions as $permission) {
                echo "<li>$permission</li>";
            }
            echo "</ul>";
            
            // Update session with fresh data
            $app->setCurrentUser([
                'id' => $user->id,
                'username' => $user->username,
                'email' => $user->email,
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'full_name' => $user->getFullName(),
                'permissions' => $permissions,
                'is_admin' => $user->hasGroup('administrators')
            ]);
            
            echo "<h2>✅ Session updated successfully!</h2>";
            
            // Test task permissions
            echo "<h3>Task permissions test:</h3>";
            $taskPermissions = ['tasks.view', 'tasks.create', 'tasks.edit', 'tasks.delete'];
            foreach ($taskPermissions as $permission) {
                $hasPermission = $app->hasPermission($permission);
                echo "<p><strong>$permission:</strong> " . ($hasPermission ? '✅ Yes' : '❌ No') . "</p>";
            }
            
            echo "<p><a href='/admin'>Go to Dashboard</a></p>";
            echo "<p><a href='/admin/tasks'>Go to Tasks</a></p>";
            
        } else {
            echo "<p style='color: red;'>User not found in database</p>";
        }
    } else {
        echo "<p style='color: red;'>User is not logged in</p>";
        echo "<p><a href='/login'>Go to Login</a></p>";
    }
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>Error</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
