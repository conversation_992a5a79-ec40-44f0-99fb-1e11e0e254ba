<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Controllers\StorageController;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'admin',
    'role' => 'admin',
    'permissions' => ['storage.view', 'storage.download', 'storage.edit', 'storage.delete', 'storage.upload', 'storage.create']
];

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

echo "<!DOCTYPE html><html><head><title>Comprehensive Storage Test</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .test { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
    .success { color: green; }
    .error { color: red; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; }
</style></head><body>";

echo "<h1>🧪 Comprehensive Storage Test</h1>";

// Test 1: Get client type
echo "<div class='test'>";
echo "<h2>Test 1: Client Type</h2>";
try {
    $controller = new StorageController();
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('getNextcloudClient');
    $method->setAccessible(true);
    $client = $method->invoke($controller);
    
    echo "<p class='success'>✅ Client type: " . get_class($client) . "</p>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// Test 2: List files
echo "<div class='test'>";
echo "<h2>Test 2: List Files in /Photos</h2>";
try {
    $files = $client->listDirectory('/Photos');
    echo "<p class='success'>✅ Found " . count($files) . " files</p>";
    echo "<ul>";
    foreach ($files as $file) {
        echo "<li>" . htmlspecialchars($file['name']) . " (" . htmlspecialchars($file['path']) . ")</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// Test 3: Check file existence
echo "<div class='test'>";
echo "<h2>Test 3: Check File Existence</h2>";
$testFiles = ['/Photos/4K_B.jpg', '/Photos/vacation.jpg', '/Photos/nonexistent.jpg'];
foreach ($testFiles as $testFile) {
    try {
        $exists = $client->exists($testFile);
        echo "<p class='" . ($exists ? 'success' : 'info') . "'>";
        echo ($exists ? "✅" : "ℹ️") . " " . htmlspecialchars($testFile) . ": " . ($exists ? "EXISTS" : "NOT FOUND");
        echo "</p>";
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error checking " . htmlspecialchars($testFile) . ": " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}
echo "</div>";

// Test 4: Download file
echo "<div class='test'>";
echo "<h2>Test 4: Download File Content</h2>";
try {
    $content = $client->downloadFile('/Photos/4K_B.jpg');
    if ($content !== false) {
        echo "<p class='success'>✅ Download successful</p>";
        echo "<p><strong>Content length:</strong> " . strlen($content) . " bytes</p>";
        echo "<p><strong>Content preview:</strong> " . htmlspecialchars(substr($content, 0, 100)) . "...</p>";
    } else {
        echo "<p class='error'>❌ Download failed</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// Test 5: Test download through controller
echo "<div class='test'>";
echo "<h2>Test 5: Download Through Controller</h2>";
try {
    // Simulate download request
    $_GET['path'] = '/Photos/4K_B.jpg';
    
    ob_start();
    $controller->download();
    $output = ob_get_contents();
    ob_end_clean();
    
    if (!empty($output)) {
        echo "<p class='success'>✅ Controller download successful</p>";
        echo "<p><strong>Output length:</strong> " . strlen($output) . " bytes</p>";
    } else {
        echo "<p class='error'>❌ Controller download failed - no output</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// Test 6: Test rename through controller
echo "<div class='test'>";
echo "<h2>Test 6: Rename Through Controller</h2>";
try {
    // Simulate rename request
    $_POST['old_path'] = '/Photos/4K_B.jpg';
    $_POST['new_name'] = '4K_Test_Renamed.jpg';
    $_POST['_token'] = $_SESSION['csrf_token'];
    
    ob_start();
    $controller->rename();
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "<p><strong>Controller response:</strong></p>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    
    $response = json_decode($output, true);
    if ($response && isset($response['success']) && $response['success']) {
        echo "<p class='success'>✅ Rename successful: " . htmlspecialchars($response['message']) . "</p>";
        
        // Verify rename
        $oldExists = $client->exists('/Photos/4K_B.jpg');
        $newExists = $client->exists('/Photos/4K_Test_Renamed.jpg');
        echo "<p>Old file exists: " . ($oldExists ? "YES" : "NO") . "</p>";
        echo "<p>New file exists: " . ($newExists ? "YES" : "NO") . "</p>";
        
    } else {
        echo "<p class='error'>❌ Rename failed: " . htmlspecialchars($response['error'] ?? 'Unknown error') . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

// Test 7: List files after rename
echo "<div class='test'>";
echo "<h2>Test 7: List Files After Rename</h2>";
try {
    $files = $client->listDirectory('/Photos');
    echo "<p class='success'>✅ Found " . count($files) . " files after rename</p>";
    echo "<ul>";
    foreach ($files as $file) {
        echo "<li>" . htmlspecialchars($file['name']) . " (" . htmlspecialchars($file['path']) . ")</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
echo "</div>";

echo "<hr>";
echo "<p><a href='/admin/storage'>← Back to Storage</a></p>";
echo "</body></html>";
