<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\UserNextcloudSettings;
use Strix\ERP\Services\NextcloudClient;
use Strix\ERP\Controllers\StorageController;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'admin',
    'role' => 'admin',
    'permissions' => ['storage.view', 'storage.download', 'storage.edit']
];

// Force real client
$_GET['force_real'] = true;

echo "<!DOCTYPE html><html><head><title>Test Directory Listing</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .section { border: 1px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .file-list { margin: 10px 0; }
    .file-item { padding: 8px; margin: 2px 0; border-radius: 3px; display: flex; align-items: center; }
    .file-item.directory { background: #e8f4fd; }
    .file-item.file { background: #f8f9fa; }
    .file-icon { margin-right: 10px; font-size: 18px; }
    .file-info { flex: 1; }
    .file-size { color: #666; font-size: 0.9em; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 300px; }
    .btn { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; text-decoration: none; display: inline-block; }
    .btn-primary { background: #007bff; color: white; }
</style></head><body>";

echo "<h1>📁 Test Directory Listing</h1>";

// Test 1: Get client and test basic listing
echo "<div class='section'>";
echo "<h2>Test 1: Basic Directory Listing</h2>";

try {
    $controller = new StorageController();
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('getNextcloudClient');
    $method->setAccessible(true);
    $client = $method->invoke($controller);
    
    $clientType = get_class($client);
    echo "<p><strong>Client type:</strong> " . $clientType . "</p>";
    
    if ($clientType === 'Strix\ERP\Services\NextcloudClient') {
        echo "<p class='success'>✅ Using real NextcloudClient</p>";
        
        // Test root directory listing
        echo "<h3>Root Directory (/) Contents:</h3>";
        $files = $client->listDirectory('/');
        
        echo "<p><strong>Total items found:</strong> " . count($files) . "</p>";
        
        if (!empty($files)) {
            $directories = array_filter($files, fn($item) => $item['is_directory']);
            $regularFiles = array_filter($files, fn($item) => !$item['is_directory']);
            
            echo "<p><strong>Directories:</strong> " . count($directories) . "</p>";
            echo "<p><strong>Files:</strong> " . count($regularFiles) . "</p>";
            
            echo "<div class='file-list'>";
            
            // Show directories first
            foreach ($directories as $item) {
                echo "<div class='file-item directory'>";
                echo "<span class='file-icon'>📁</span>";
                echo "<div class='file-info'>";
                echo "<strong>" . htmlspecialchars($item['name']) . "</strong>";
                echo "<div class='file-size'>Directory • " . htmlspecialchars($item['path']) . "</div>";
                echo "</div>";
                echo "</div>";
            }
            
            // Then show files
            foreach ($regularFiles as $item) {
                echo "<div class='file-item file'>";
                echo "<span class='file-icon'>📄</span>";
                echo "<div class='file-info'>";
                echo "<strong>" . htmlspecialchars($item['name']) . "</strong>";
                echo "<div class='file-size'>" . number_format($item['size']) . " bytes • " . htmlspecialchars($item['path']) . "</div>";
                echo "</div>";
                echo "</div>";
            }
            
            echo "</div>";
            
            // Test listing a subdirectory if available
            if (!empty($directories)) {
                $testDir = $directories[0];
                echo "<h3>Testing Subdirectory: " . htmlspecialchars($testDir['name']) . "</h3>";
                
                try {
                    $subFiles = $client->listDirectory($testDir['path']);
                    echo "<p class='success'>✅ Subdirectory listing successful (" . count($subFiles) . " items)</p>";
                    
                    if (!empty($subFiles)) {
                        echo "<div class='file-list'>";
                        foreach (array_slice($subFiles, 0, 5) as $item) {
                            $icon = $item['is_directory'] ? '📁' : '📄';
                            $type = $item['is_directory'] ? 'Directory' : number_format($item['size']) . ' bytes';
                            echo "<div class='file-item " . ($item['is_directory'] ? 'directory' : 'file') . "'>";
                            echo "<span class='file-icon'>$icon</span>";
                            echo "<div class='file-info'>";
                            echo "<strong>" . htmlspecialchars($item['name']) . "</strong>";
                            echo "<div class='file-size'>$type • " . htmlspecialchars($item['path']) . "</div>";
                            echo "</div>";
                            echo "</div>";
                        }
                        if (count($subFiles) > 5) {
                            echo "<p><em>... and " . (count($subFiles) - 5) . " more items</em></p>";
                        }
                        echo "</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<p class='error'>❌ Subdirectory listing failed: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }
            
        } else {
            echo "<p class='warning'>⚠️ No items found in root directory</p>";
        }
        
    } else {
        echo "<p class='warning'>⚠️ Using MockNextcloudClient - cannot test real directories</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Test 2: Check logs for debugging
echo "<div class='section'>";
echo "<h2>Test 2: Debug Logs</h2>";

$logFile = __DIR__ . '/../logs/nextcloud.log';
if (file_exists($logFile)) {
    $logs = file_get_contents($logFile);
    $lines = explode("\n", array_reverse(explode("\n", $logs))[0] ?? '');
    $recentLines = array_slice(array_filter($lines), 0, 20);
    
    if (!empty($recentLines)) {
        echo "<p><strong>Recent log entries:</strong></p>";
        echo "<pre>";
        foreach ($recentLines as $line) {
            if (strpos($line, 'Parsed item:') !== false || strpos($line, 'Found') !== false || strpos($line, 'Skipping') !== false) {
                echo htmlspecialchars($line) . "\n";
            }
        }
        echo "</pre>";
    } else {
        echo "<p class='info'>No relevant log entries found</p>";
    }
} else {
    echo "<p class='info'>No log file found</p>";
}

echo "</div>";

// Test 3: Raw WebDAV response
echo "<div class='section'>";
echo "<h2>Test 3: Raw WebDAV Response (Debug)</h2>";

if (isset($client) && get_class($client) === 'Strix\ERP\Services\NextcloudClient') {
    try {
        $userSettings = UserNextcloudSettings::getActiveForUser(1);
        if ($userSettings) {
            $decryptedPassword = $userSettings->getDecryptedPassword();
            
            if ($decryptedPassword) {
                echo "<p class='info'>Making raw WebDAV PROPFIND request...</p>";
                
                $ch = curl_init();
                $url = rtrim($userSettings->server_url, '/') . '/remote.php/dav/files/' . $userSettings->username . '/';
                
                curl_setopt_array($ch, [
                    CURLOPT_URL => $url,
                    CURLOPT_CUSTOMREQUEST => 'PROPFIND',
                    CURLOPT_HTTPHEADER => [
                        'Authorization: Basic ' . base64_encode($userSettings->username . ':' . $decryptedPassword),
                        'Content-Type: application/xml',
                        'Depth: 1'
                    ],
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_TIMEOUT => 30,
                    CURLOPT_SSL_VERIFYPEER => $userSettings->verify_ssl ?? true,
                    CURLOPT_POSTFIELDS => '<?xml version="1.0"?>
                        <d:propfind xmlns:d="DAV:" xmlns:oc="http://owncloud.org/ns">
                            <d:prop>
                                <d:displayname />
                                <d:getcontenttype />
                                <d:getlastmodified />
                                <d:resourcetype />
                                <oc:size />
                                <oc:permissions />
                            </d:prop>
                        </d:propfind>'
                ]);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);
                
                if ($error) {
                    echo "<p class='error'>❌ cURL error: " . htmlspecialchars($error) . "</p>";
                } elseif ($httpCode >= 400) {
                    echo "<p class='error'>❌ HTTP error: $httpCode</p>";
                } else {
                    echo "<p class='success'>✅ Raw WebDAV response received (HTTP $httpCode)</p>";
                    echo "<p><strong>Response length:</strong> " . strlen($response) . " characters</p>";
                    
                    // Show first part of response
                    echo "<p><strong>Response preview:</strong></p>";
                    echo "<pre>" . htmlspecialchars(substr($response, 0, 1000)) . "</pre>";
                    if (strlen($response) > 1000) {
                        echo "<p><em>... (truncated)</em></p>";
                    }
                }
            } else {
                echo "<p class='error'>❌ No decrypted password available</p>";
            }
        } else {
            echo "<p class='error'>❌ No user settings found</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

echo "</div>";

echo "<div class='section'>";
echo "<h2>Quick Actions</h2>";
echo "<a href='/admin/storage?force_real=1' class='btn btn-primary'>📁 Open Storage (Real Client)</a>";
echo "<a href='/view_nextcloud_logs.php' class='btn btn-primary'>📋 View Full Logs</a>";
echo "<a href='/check_user_nextcloud_settings.php' class='btn btn-primary'>🔍 Check Settings</a>";
echo "</div>";

echo "</body></html>";
