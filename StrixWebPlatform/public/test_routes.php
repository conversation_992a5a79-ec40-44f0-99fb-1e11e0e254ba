<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Routes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 8px 15px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Test Routes</h1>
    
    <h2>Test Nextcloud Settings Routes</h2>
    
    <button class="btn-primary" onclick="testRoute('GET', '/admin/profile/nextcloud-settings')">
        GET /admin/profile/nextcloud-settings
    </button>
    
    <button class="btn-success" onclick="testRoute('POST', '/admin/profile/nextcloud-settings/update')">
        POST /admin/profile/nextcloud-settings/update
    </button>
    
    <button class="btn-info" onclick="testRoute('POST', '/admin/profile/nextcloud-settings/test')">
        POST /admin/profile/nextcloud-settings/test
    </button>
    
    <button class="btn-primary" onclick="testRoute('GET', '/admin/profile/nextcloud-settings/get')">
        GET /admin/profile/nextcloud-settings/get
    </button>
    
    <div id="result"></div>
    
    <h2>Test Other Routes</h2>
    
    <button class="btn-primary" onclick="testRoute('GET', '/admin/profile')">
        GET /admin/profile
    </button>
    
    <button class="btn-primary" onclick="testRoute('GET', '/admin/storage')">
        GET /admin/storage
    </button>
    
    <script>
    function testRoute(method, url) {
        const result = document.getElementById('result');
        result.innerHTML = '<p class="info">Testing ' + method + ' ' + url + '...</p>';
        
        const options = {
            method: method,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        if (method === 'POST') {
            const formData = new FormData();
            formData.append('_token', '<?= $_SESSION['csrf_token'] ?>');
            
            // Add test data for update route
            if (url.includes('/update')) {
                formData.append('server_url', 'https://demo.nextcloud.com');
                formData.append('username', 'testuser');
                formData.append('password', 'testpass');
            }
            
            options.body = formData;
        }
        
        fetch(url, options)
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', [...response.headers.entries()]);
            
            const contentType = response.headers.get('content-type');
            
            if (contentType && contentType.includes('application/json')) {
                return response.json().then(data => ({
                    status: response.status,
                    type: 'json',
                    data: data
                }));
            } else {
                return response.text().then(text => ({
                    status: response.status,
                    type: 'text',
                    data: text
                }));
            }
        })
        .then(result_data => {
            let html = '<h3>Response for ' + method + ' ' + url + '</h3>';
            html += '<p><strong>Status:</strong> ' + result_data.status + '</p>';
            html += '<p><strong>Type:</strong> ' + result_data.type + '</p>';
            
            if (result_data.type === 'json') {
                html += '<pre>' + JSON.stringify(result_data.data, null, 2) + '</pre>';
            } else {
                // Show first 500 characters of HTML response
                const preview = result_data.data.substring(0, 500);
                html += '<pre>' + escapeHtml(preview) + (result_data.data.length > 500 ? '...' : '') + '</pre>';
            }
            
            result.innerHTML = html;
        })
        .catch(error => {
            console.error('Fetch error:', error);
            result.innerHTML = '<p class="error">❌ Error: ' + error.message + '</p>';
        });
    }
    
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    </script>
</body>
</html>
