<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Simple Nextcloud Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; background: #007bff; color: white; }
        #result { margin-top: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>🧪 Simple Nextcloud Settings Test</h1>
    
    <p>CSRF Token: <code><?= $_SESSION['csrf_token'] ?></code></p>
    
    <h2>Test Form</h2>
    <form id="testForm">
        <input type="hidden" name="_token" value="<?= $_SESSION['csrf_token'] ?>">
        
        <p>
            <label>Server URL:</label><br>
            <input type="url" name="server_url" value="https://demo.nextcloud.com" style="width: 300px;">
        </p>
        
        <p>
            <label>Username:</label><br>
            <input type="text" name="username" value="testuser" style="width: 300px;">
        </p>
        
        <p>
            <label>Password:</label><br>
            <input type="password" name="password" value="testpass" style="width: 300px;">
        </p>
        
        <p>
            <button type="submit">💾 Save Settings</button>
            <button type="button" onclick="testConnection()">🔄 Test Connection</button>
        </p>
    </form>
    
    <div id="result"></div>
    
    <h2>Debug Info</h2>
    <p><strong>Current URL:</strong> <?= $_SERVER['REQUEST_URI'] ?></p>
    <p><strong>User ID:</strong> <?= $app->getCurrentUser()['id'] ?></p>
    <p><strong>Permissions:</strong></p>
    <ul>
        <li>nextcloud.view_personal: <?= $app->hasPermission('nextcloud.view_personal') ? 'Yes' : 'No' ?></li>
        <li>nextcloud.manage_personal: <?= $app->hasPermission('nextcloud.manage_personal') ? 'Yes' : 'No' ?></li>
        <li>nextcloud.test_connection: <?= $app->hasPermission('nextcloud.test_connection') ? 'Yes' : 'No' ?></li>
    </ul>
    
    <script>
    // Enable detailed logging
    console.log('Script loaded');
    
    document.getElementById('testForm').addEventListener('submit', function(e) {
        e.preventDefault();
        console.log('Form submitted');
        
        const result = document.getElementById('result');
        result.innerHTML = '<p class="info">💾 Saving settings...</p>';
        
        const formData = new FormData(this);
        
        console.log('FormData contents:');
        for (let [key, value] of formData.entries()) {
            console.log(key + ': ' + value);
        }
        
        console.log('Sending request to /admin/profile/nextcloud-settings/update');
        
        fetch('/admin/profile/nextcloud-settings/update', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Response received:', response);
            console.log('Status:', response.status);
            console.log('Status text:', response.statusText);
            console.log('Headers:');
            for (let [key, value] of response.headers.entries()) {
                console.log('  ' + key + ': ' + value);
            }
            
            const contentType = response.headers.get('content-type');
            console.log('Content-Type:', contentType);
            
            return response.text().then(text => {
                console.log('Response text:', text);

                if (contentType && contentType.includes('application/json')) {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        throw new Error('Invalid JSON: ' + text.substring(0, 200));
                    }
                } else {
                    // Show the HTML response for debugging
                    throw new Error('Expected JSON response, got HTML. Response: ' + text.substring(0, 500));
                }
            });
        })
        .then(data => {
            console.log('Parsed data:', data);
            
            if (data.success) {
                result.innerHTML = '<p class="success">✅ ' + data.message + '</p>';
            } else {
                result.innerHTML = '<p class="error">❌ ' + data.error + '</p>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            result.innerHTML = '<p class="error">❌ Error: ' + error.message + '</p>';
        });
    });
    
    function testConnection() {
        console.log('Test connection clicked');
        
        const result = document.getElementById('result');
        result.innerHTML = '<p class="info">🔄 Testing connection...</p>';
        
        const formData = new FormData();
        formData.append('_token', '<?= $_SESSION['csrf_token'] ?>');
        
        console.log('Sending test request to /admin/profile/nextcloud-settings/test');
        
        fetch('/admin/profile/nextcloud-settings/test', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Test response received:', response);
            console.log('Test status:', response.status);

            const contentType = response.headers.get('content-type');
            console.log('Test Content-Type:', contentType);

            return response.text().then(text => {
                console.log('Test response text:', text);

                if (contentType && contentType.includes('application/json')) {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        throw new Error('Invalid JSON: ' + text.substring(0, 200));
                    }
                } else {
                    // Show the HTML response for debugging
                    throw new Error('Expected JSON response, got HTML. Response: ' + text.substring(0, 500));
                }
            });
        })
        .then(data => {
            console.log('Test parsed data:', data);
            
            if (data.success) {
                result.innerHTML = '<p class="success">✅ ' + data.message + '</p>';
            } else {
                result.innerHTML = '<p class="error">❌ ' + data.error + '</p>';
            }
        })
        .catch(error => {
            console.error('Test error:', error);
            result.innerHTML = '<p class="error">❌ Test error: ' + error.message + '</p>';
        });
    }
    
    console.log('All event listeners attached');
    </script>
</body>
</html>
