<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Controllers\UserSettingsController;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Set up POST data
$_POST = [
    '_token' => $_SESSION['csrf_token'],
    'server_url' => 'https://demo.nextcloud.com',
    'username' => 'testuser',
    'password' => 'testpass',
    'verify_ssl' => '1',
    'timeout' => '30',
    'default_folder' => '/ERP_Test',
    'auto_create_folders' => '1'
];

echo "Testing JSON output...\n";
echo "Output buffer level before: " . ob_get_level() . "\n";

// Check if there's any output before we start
if (ob_get_length() > 0) {
    echo "WARNING: There's already output in the buffer!\n";
    echo "Buffer content: " . ob_get_contents() . "\n";
}

// Clear any existing output
while (ob_get_level() > 0) {
    ob_end_clean();
}

echo "Output buffer level after cleanup: " . ob_get_level() . "\n";

// Start fresh output buffering
ob_start();

try {
    $controller = new UserSettingsController();
    
    echo "About to call updateNextcloudSettings...\n";
    
    // Call the method
    $controller->updateNextcloudSettings();
    
    echo "Method call completed.\n";
    
} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}

// Get the output
$output = ob_get_clean();

echo "\n=== ANALYSIS ===\n";
echo "Output length: " . strlen($output) . "\n";
echo "Output (raw): " . var_export($output, true) . "\n";
echo "Output (hex): " . bin2hex($output) . "\n";

if (!empty($output)) {
    // Check if it's valid JSON
    $decoded = json_decode($output, true);
    if ($decoded !== null) {
        echo "✅ Valid JSON\n";
        echo "Decoded: " . print_r($decoded, true) . "\n";
    } else {
        echo "❌ Invalid JSON\n";
        echo "JSON error: " . json_last_error_msg() . "\n";
        
        // Check for common issues
        if (strpos($output, '<') !== false) {
            echo "⚠️ Output contains HTML tags\n";
        }
        
        if (preg_match('/^\s+/', $output)) {
            echo "⚠️ Output starts with whitespace\n";
        }
        
        if (preg_match('/\s+$/', $output)) {
            echo "⚠️ Output ends with whitespace\n";
        }
    }
}

echo "\n=== HEADERS ===\n";
$headers = headers_list();
foreach ($headers as $header) {
    echo "$header\n";
}

echo "\n=== RESPONSE CODE ===\n";
echo "HTTP Response Code: " . http_response_code() . "\n";
?>
