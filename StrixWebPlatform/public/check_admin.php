<?php

/**
 * Check admin status
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\User;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

header('Content-Type: text/html; charset=UTF-8');

try {
    echo "<h1>🔍 Check Admin Status</h1>";
    
    // Initialize application
    $app = Application::getInstance();
    
    if ($app->isLoggedIn()) {
        $currentUser = $app->getCurrentUser();
        echo "<h2>Current Session User:</h2>";
        echo "<pre>" . print_r($currentUser, true) . "</pre>";
        
        echo "<h2>Admin Status Check:</h2>";
        echo "<p><strong>is_admin in session:</strong> " . ($currentUser['is_admin'] ? 'true' : 'false') . "</p>";
        
        // Check from database
        $user = User::find($currentUser['id']);
        if ($user) {
            $hasAdminGroup = $user->hasGroup('administrators');
            echo "<p><strong>hasGroup('administrators') from DB:</strong> " . ($hasAdminGroup ? 'true' : 'false') . "</p>";
            
            $groups = $user->getGroups();
            echo "<h3>User Groups:</h3>";
            echo "<ul>";
            foreach ($groups as $group) {
                echo "<li>{$group->name} (ID: {$group->id})</li>";
            }
            echo "</ul>";
            
            // Force update session
            echo "<h2>🔄 Updating session...</h2>";
            $permissions = $user->getPermissionNames();
            
            $app->setCurrentUser([
                'id' => $user->id,
                'username' => $user->username,
                'email' => $user->email,
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'full_name' => $user->getFullName(),
                'permissions' => $permissions,
                'is_admin' => $user->hasGroup('administrators')
            ]);
            
            echo "<p>✅ Session updated!</p>";
            
            // Check again
            $updatedUser = $app->getCurrentUser();
            echo "<p><strong>is_admin after update:</strong> " . ($updatedUser['is_admin'] ? 'true' : 'false') . "</p>";
            
            echo "<h3>Task permissions:</h3>";
            $taskPermissions = ['tasks.view', 'tasks.create', 'tasks.edit', 'tasks.delete'];
            foreach ($taskPermissions as $permission) {
                $hasPermission = $app->hasPermission($permission);
                echo "<p><strong>$permission:</strong> " . ($hasPermission ? '✅' : '❌') . "</p>";
            }
            
            echo "<p><a href='/admin'>Go to Dashboard</a></p>";
            echo "<p><a href='/admin/tasks'>Go to Tasks</a></p>";
            
        } else {
            echo "<p style='color: red;'>User not found in database</p>";
        }
    } else {
        echo "<p style='color: red;'>User is not logged in</p>";
        echo "<p><a href='/login'>Go to Login</a></p>";
    }
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>Error</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
