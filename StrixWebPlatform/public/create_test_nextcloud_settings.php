<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\UserNextcloudSettings;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'admin',
    'role' => 'admin',
    'permissions' => ['storage.view', 'storage.download', 'storage.edit']
];

echo "<!DOCTYPE html><html><head><title>Create Test Nextcloud Settings</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .section { border: 1px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    .form-group { margin: 15px 0; }
    .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
    .form-group input, .form-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px; }
    .btn { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
    .btn-primary { background: #007bff; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-danger { background: #dc3545; color: white; }
</style></head><body>";

echo "<h1>🔧 Create Test Nextcloud Settings</h1>";

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<div class='section'>";
    echo "<h2>Creating Settings...</h2>";
    
    try {
        $data = [
            'server_url' => $_POST['server_url'],
            'username' => $_POST['username'],
            'password' => $_POST['password'],
            'verify_ssl' => isset($_POST['verify_ssl']) ? 1 : 0,
            'timeout' => (int)$_POST['timeout'],
            'default_folder' => $_POST['default_folder'],
            'auto_create_folders' => isset($_POST['auto_create_folders']) ? 1 : 0
        ];
        
        $settings = UserNextcloudSettings::createOrUpdate(1, $data);
        
        echo "<p class='success'>✅ Settings created successfully!</p>";
        echo "<p><strong>Settings ID:</strong> " . $settings->id . "</p>";
        
        // Test the connection
        echo "<h3>Testing Connection...</h3>";
        $testResult = $settings->testConnection();
        
        if ($testResult['success']) {
            echo "<p class='success'>✅ Connection test successful!</p>";
            echo "<p><strong>HTTP Code:</strong> " . $testResult['http_code'] . "</p>";
        } else {
            echo "<p class='error'>❌ Connection test failed</p>";
            echo "<p><strong>Error:</strong> " . htmlspecialchars($testResult['error']) . "</p>";
        }
        
        echo "<p><a href='/check_user_nextcloud_settings.php'>🔍 Check Settings</a></p>";
        echo "<p><a href='/test_real_client.php'>🧪 Test Real Client</a></p>";
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error creating settings: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
} else {
    // Show current settings
    echo "<div class='section'>";
    echo "<h2>Current Settings</h2>";
    
    try {
        $currentSettings = UserNextcloudSettings::getActiveForUser(1);
        
        if ($currentSettings) {
            echo "<p class='info'>ℹ️ User already has active Nextcloud settings</p>";
            echo "<p><strong>Server URL:</strong> " . htmlspecialchars($currentSettings->server_url) . "</p>";
            echo "<p><strong>Username:</strong> " . htmlspecialchars($currentSettings->username) . "</p>";
            echo "<p><strong>Last Test:</strong> " . ($currentSettings->last_test_result ?: 'Never tested') . "</p>";
            
            echo "<p><a href='/check_user_nextcloud_settings.php'>🔍 Check Current Settings</a></p>";
        } else {
            echo "<p class='warning'>⚠️ No active Nextcloud settings found for user</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error checking current settings: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
    
    // Show form
    echo "<div class='section'>";
    echo "<h2>Create New Settings</h2>";
    
    // Pre-fill with global config values if they exist
    $globalConfig = require __DIR__ . '/../config/nextcloud.php';
    $defaultUrl = $globalConfig['server']['url'];
    $defaultUsername = $globalConfig['auth']['username'];
    
    // Don't use placeholder values
    if (in_array($defaultUrl, ['https://your-nextcloud-server.com', ''])) {
        $defaultUrl = '';
    }
    if (in_array($defaultUsername, ['your-username', ''])) {
        $defaultUsername = '';
    }
    
    echo "<form method='POST'>";
    
    echo "<div class='form-group'>";
    echo "<label for='server_url'>Server URL *</label>";
    echo "<input type='url' id='server_url' name='server_url' value='" . htmlspecialchars($defaultUrl) . "' required>";
    echo "<small>Example: https://cloud.example.com</small>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label for='username'>Username *</label>";
    echo "<input type='text' id='username' name='username' value='" . htmlspecialchars($defaultUsername) . "' required>";
    echo "<small>Your Nextcloud username</small>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label for='password'>App Password *</label>";
    echo "<input type='password' id='password' name='password' required>";
    echo "<small>Generate an app password in Nextcloud Settings > Security</small>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label for='timeout'>Timeout (seconds)</label>";
    echo "<input type='number' id='timeout' name='timeout' value='30' min='5' max='300'>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label for='default_folder'>Default Folder</label>";
    echo "<input type='text' id='default_folder' name='default_folder' value='/ERP_Files'>";
    echo "<small>Default folder for ERP file uploads</small>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>";
    echo "<input type='checkbox' name='verify_ssl' checked> Verify SSL Certificate";
    echo "</label>";
    echo "<small>Uncheck only for testing with self-signed certificates</small>";
    echo "</div>";
    
    echo "<div class='form-group'>";
    echo "<label>";
    echo "<input type='checkbox' name='auto_create_folders' checked> Auto-create folders";
    echo "</label>";
    echo "<small>Automatically create folders if they don't exist</small>";
    echo "</div>";
    
    echo "<button type='submit' class='btn btn-primary'>Create Settings</button>";
    echo "</form>";
    
    echo "</div>";
    
    // Show example values
    echo "<div class='section'>";
    echo "<h2>Example Configuration</h2>";
    echo "<p>Here's an example of what the settings should look like:</p>";
    echo "<ul>";
    echo "<li><strong>Server URL:</strong> https://cloud.example.com (your actual Nextcloud server)</li>";
    echo "<li><strong>Username:</strong> your_nextcloud_username</li>";
    echo "<li><strong>App Password:</strong> Generate this in Nextcloud: Settings > Security > App passwords</li>";
    echo "<li><strong>Timeout:</strong> 30 seconds (adjust based on your server)</li>";
    echo "<li><strong>Default Folder:</strong> /ERP_Files (will be created automatically)</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<div class='section'>";
echo "<h2>Quick Links</h2>";
echo "<a href='/check_user_nextcloud_settings.php' class='btn btn-primary'>🔍 Check Settings</a>";
echo "<a href='/test_real_client.php' class='btn btn-primary'>🧪 Test Real Client</a>";
echo "<a href='/admin/user-settings' class='btn btn-primary'>📝 User Settings Page</a>";
echo "<a href='/admin/storage' class='btn btn-primary'>📁 Storage Page</a>";
echo "</div>";

echo "</body></html>";
