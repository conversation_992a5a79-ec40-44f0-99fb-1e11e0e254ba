<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\UserNextcloudSettings;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

// Generate CSRF token if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$currentUser = $app->getCurrentUser();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Nextcloud Settings</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { margin: 5px; padding: 8px 15px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
    </style>
</head>
<body>
    <h1>🔧 Debug Nextcloud Settings</h1>
    
    <div class="test-section">
        <h2>🔐 Session & CSRF</h2>
        <table border="1" cellpadding="5" cellspacing="0">
            <tr>
                <td><strong>Session ID:</strong></td>
                <td><?= session_id() ?></td>
            </tr>
            <tr>
                <td><strong>CSRF Token:</strong></td>
                <td><?= $_SESSION['csrf_token'] ?? 'Not set' ?></td>
            </tr>
            <tr>
                <td><strong>User ID:</strong></td>
                <td><?= $currentUser['id'] ?></td>
            </tr>
            <tr>
                <td><strong>Username:</strong></td>
                <td><?= htmlspecialchars($currentUser['username']) ?></td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>🔐 Permissions</h2>
        <table border="1" cellpadding="5" cellspacing="0">
            <tr>
                <td><strong>nextcloud.view_personal:</strong></td>
                <td class="<?= $app->hasPermission('nextcloud.view_personal') ? 'success' : 'error' ?>">
                    <?= $app->hasPermission('nextcloud.view_personal') ? '✅ Yes' : '❌ No' ?>
                </td>
            </tr>
            <tr>
                <td><strong>nextcloud.manage_personal:</strong></td>
                <td class="<?= $app->hasPermission('nextcloud.manage_personal') ? 'success' : 'error' ?>">
                    <?= $app->hasPermission('nextcloud.manage_personal') ? '✅ Yes' : '❌ No' ?>
                </td>
            </tr>
            <tr>
                <td><strong>nextcloud.test_connection:</strong></td>
                <td class="<?= $app->hasPermission('nextcloud.test_connection') ? 'success' : 'error' ?>">
                    <?= $app->hasPermission('nextcloud.test_connection') ? '✅ Yes' : '❌ No' ?>
                </td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>🧪 Test API Endpoints</h2>
        
        <h3>Test Save Settings</h3>
        <button class="btn-success" onclick="testSaveSettings()">Test Save</button>
        <div id="saveResult"></div>
        
        <h3>Test Connection</h3>
        <button class="btn-info" onclick="testConnection()">Test Connection</button>
        <div id="testResult"></div>
        
        <h3>Get Settings</h3>
        <button class="btn-primary" onclick="getSettings()">Get Settings</button>
        <div id="getResult"></div>
    </div>
    
    <div class="test-section">
        <h2>📊 Current Settings</h2>
        <?php
        $settings = UserNextcloudSettings::getActiveForUser($currentUser['id']);
        if ($settings):
        ?>
            <pre><?= print_r($settings, true) ?></pre>
        <?php else: ?>
            <p class="warning">No settings found</p>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>🌐 Network Test</h2>
        <button class="btn-warning" onclick="testNetworkConnectivity()">Test Network</button>
        <div id="networkResult"></div>
    </div>
    
    <script>
    function testSaveSettings() {
        const result = document.getElementById('saveResult');
        result.innerHTML = '<p class="info">Testing save settings...</p>';
        
        const formData = new FormData();
        formData.append('_token', '<?= $_SESSION['csrf_token'] ?>');
        formData.append('server_url', 'https://demo.nextcloud.com');
        formData.append('username', 'testuser');
        formData.append('password', 'testpass');
        formData.append('verify_ssl', '1');
        formData.append('timeout', '30');
        formData.append('default_folder', '/ERP_Test');
        formData.append('auto_create_folders', '1');
        
        fetch('/admin/profile/nextcloud-settings/update', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            return response.text();
        })
        .then(text => {
            console.log('Response text:', text);
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    result.innerHTML = '<p class="success">✅ ' + data.message + '</p>';
                } else {
                    result.innerHTML = '<p class="error">❌ ' + data.error + '</p>';
                }
            } catch (e) {
                result.innerHTML = '<p class="error">❌ Invalid JSON response: ' + text + '</p>';
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            result.innerHTML = '<p class="error">❌ Network error: ' + error.message + '</p>';
        });
    }
    
    function testConnection() {
        const result = document.getElementById('testResult');
        result.innerHTML = '<p class="info">Testing connection...</p>';
        
        const formData = new FormData();
        formData.append('_token', '<?= $_SESSION['csrf_token'] ?>');
        
        fetch('/admin/profile/nextcloud-settings/test', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Test response status:', response.status);
            return response.text();
        })
        .then(text => {
            console.log('Test response text:', text);
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    result.innerHTML = '<p class="success">✅ ' + data.message + '</p>';
                } else {
                    result.innerHTML = '<p class="error">❌ ' + data.error + '</p>';
                }
            } catch (e) {
                result.innerHTML = '<p class="error">❌ Invalid JSON response: ' + text + '</p>';
            }
        })
        .catch(error => {
            console.error('Test fetch error:', error);
            result.innerHTML = '<p class="error">❌ Network error: ' + error.message + '</p>';
        });
    }
    
    function getSettings() {
        const result = document.getElementById('getResult');
        result.innerHTML = '<p class="info">Getting settings...</p>';
        
        fetch('/admin/profile/nextcloud-settings/get', {
            method: 'GET'
        })
        .then(response => {
            console.log('Get response status:', response.status);
            return response.text();
        })
        .then(text => {
            console.log('Get response text:', text);
            try {
                const data = JSON.parse(text);
                result.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (e) {
                result.innerHTML = '<p class="error">❌ Invalid JSON response: ' + text + '</p>';
            }
        })
        .catch(error => {
            console.error('Get fetch error:', error);
            result.innerHTML = '<p class="error">❌ Network error: ' + error.message + '</p>';
        });
    }
    
    function testNetworkConnectivity() {
        const result = document.getElementById('networkResult');
        result.innerHTML = '<p class="info">Testing network connectivity...</p>';
        
        // Test if we can reach the admin page
        fetch('/admin', {
            method: 'GET'
        })
        .then(response => {
            if (response.ok) {
                result.innerHTML = '<p class="success">✅ Network connectivity OK</p>';
            } else {
                result.innerHTML = '<p class="error">❌ Network issue: ' + response.status + '</p>';
            }
        })
        .catch(error => {
            result.innerHTML = '<p class="error">❌ Network error: ' + error.message + '</p>';
        });
    }
    </script>
    
    <p><a href="/admin/profile/nextcloud-settings">← Back to Nextcloud Settings</a></p>
</body>
</html>
