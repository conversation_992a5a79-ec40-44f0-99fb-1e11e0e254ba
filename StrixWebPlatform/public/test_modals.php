<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modals</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>🧪 Test Bootstrap Modals</h1>
        <p>Тестване на модалните прозорци с Bootstrap 5</p>
        
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h5>Upload Modal</h5>
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#uploadModal">
                            📤 Качи файлове
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h5>Create Folder Modal</h5>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createFolderModal">
                            📁 Нова папка
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <h5>Rename Modal</h5>
                        <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#renameModal" onclick="prepareRename('/test/file.txt', 'file.txt')">
                            ✏️ Преименувай
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="/admin/storage?force_real=1" class="btn btn-outline-primary">
                🔙 Обратно към Storage
            </a>
        </div>
    </div>

    <!-- Upload Modal -->
    <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <h5 class="modal-title" id="uploadModalLabel">Качване на файлове</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="upload-drop-zone" style="border: 2px dashed #dee2e6; border-radius: 8px; padding: 2rem; text-align: center; background: #f8f9fa;">
                        <div style="font-size: 3rem; color: #6c757d; margin-bottom: 1rem;">📤</div>
                        <h5>Пуснете файловете тук</h5>
                        <p class="text-muted mb-2">или кликнете за да изберете файлове</p>
                        <small class="text-muted">Поддържа множество файлове</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отказ</button>
                    <button type="button" class="btn btn-success">Качи файлове</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Folder Modal -->
    <div class="modal fade" id="createFolderModal" tabindex="-1" aria-labelledby="createFolderModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <h5 class="modal-title" id="createFolderModalLabel">Създаване на нова папка</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="folderName" class="form-label">Име на папката:</label>
                        <input type="text" id="folderName" class="form-control" placeholder="Въведете име на папката">
                        <div class="form-text">Името не може да съдържа: / \ : * ? " &lt; &gt; |</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отказ</button>
                    <button type="button" class="btn btn-primary">Създай папка</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Rename Modal -->
    <div class="modal fade" id="renameModal" tabindex="-1" aria-labelledby="renameModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                    <h5 class="modal-title" id="renameModalLabel">Преименуване</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="newName" class="form-label">Ново име:</label>
                        <input type="text" id="newName" class="form-control" placeholder="Въведете новото име">
                        <div class="form-text">Името не може да съдържа: / \ : * ? " &lt; &gt; |</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отказ</button>
                    <button type="button" class="btn btn-warning">Преименувай</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function prepareRename(path, currentName) {
            document.getElementById('newName').value = currentName;
        }
        
        // Test modal events
        document.addEventListener('DOMContentLoaded', function() {
            const modals = ['uploadModal', 'createFolderModal', 'renameModal'];
            
            modals.forEach(modalId => {
                const modalEl = document.getElementById(modalId);
                if (modalEl) {
                    modalEl.addEventListener('show.bs.modal', function() {
                        console.log(`${modalId} is about to show`);
                    });
                    
                    modalEl.addEventListener('shown.bs.modal', function() {
                        console.log(`${modalId} is now visible`);
                    });
                    
                    modalEl.addEventListener('hide.bs.modal', function() {
                        console.log(`${modalId} is about to hide`);
                    });
                    
                    modalEl.addEventListener('hidden.bs.modal', function() {
                        console.log(`${modalId} is now hidden`);
                    });
                }
            });
        });
    </script>
</body>
</html>
