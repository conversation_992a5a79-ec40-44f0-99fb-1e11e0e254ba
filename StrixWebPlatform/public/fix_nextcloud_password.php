<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\UserNextcloudSettings;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

// Mock login
$_SESSION['user_id'] = 1;
$_SESSION['user_role'] = 'admin';
$_SESSION['user'] = [
    'id' => 1,
    'username' => 'admin',
    'role' => 'admin',
    'permissions' => ['storage.view', 'storage.download', 'storage.edit']
];

echo "<!DOCTYPE html><html><head><title>Fix Nextcloud Password</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .section { border: 1px solid #ddd; margin: 15px 0; padding: 20px; border-radius: 8px; }
    .success { color: green; font-weight: bold; }
    .error { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    .info { color: blue; }
    pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    .form-group { margin: 15px 0; }
    .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
    .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px; }
    .btn { padding: 10px 15px; margin: 5px; border: none; border-radius: 3px; cursor: pointer; }
    .btn-primary { background: #007bff; color: white; }
    .btn-success { background: #28a745; color: white; }
    .btn-danger { background: #dc3545; color: white; }
</style></head><body>";

echo "<h1>🔧 Fix Nextcloud Password</h1>";

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['new_password'])) {
    echo "<div class='section'>";
    echo "<h2>Updating Password</h2>";
    
    $newPassword = $_POST['new_password'];
    
    try {
        $settings = UserNextcloudSettings::getActiveForUser(1);
        
        if (!$settings) {
            echo "<p class='error'>❌ No active settings found</p>";
        } else {
            echo "<p class='info'>Found settings with ID: " . $settings->id . "</p>";
            
            // Update password using the setEncryptedPassword method
            $settings->setEncryptedPassword($newPassword);
            
            // Save the settings
            if ($settings->save()) {
                echo "<p class='success'>✅ Password updated successfully!</p>";
                
                // Verify the update
                $updatedSettings = UserNextcloudSettings::getActiveForUser(1);
                $decryptedPassword = $updatedSettings->getDecryptedPassword();
                
                if ($decryptedPassword === $newPassword) {
                    echo "<p class='success'>✅ Password verification successful!</p>";
                    
                    // Test connection
                    echo "<h3>Testing Connection:</h3>";
                    $testResult = $updatedSettings->testConnection();
                    
                    if ($testResult['success']) {
                        echo "<p class='success'>✅ Connection test successful!</p>";
                        echo "<p><strong>HTTP Code:</strong> " . $testResult['http_code'] . "</p>";
                    } else {
                        echo "<p class='error'>❌ Connection test failed</p>";
                        echo "<p><strong>Error:</strong> " . htmlspecialchars($testResult['error']) . "</p>";
                    }
                    
                } else {
                    echo "<p class='error'>❌ Password verification failed</p>";
                    echo "<p><strong>Expected:</strong> " . htmlspecialchars($newPassword) . "</p>";
                    echo "<p><strong>Got:</strong> " . htmlspecialchars($decryptedPassword ?: 'NULL') . "</p>";
                }
                
            } else {
                echo "<p class='error'>❌ Failed to save password</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
}

// Handle delete settings
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_settings'])) {
    echo "<div class='section'>";
    echo "<h2>Deleting Settings</h2>";
    
    try {
        $settings = UserNextcloudSettings::getActiveForUser(1);
        
        if (!$settings) {
            echo "<p class='warning'>⚠️ No active settings to delete</p>";
        } else {
            if ($settings->deleteSettings()) {
                echo "<p class='success'>✅ Settings deleted successfully!</p>";
            } else {
                echo "<p class='error'>❌ Failed to delete settings</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
}

// Show current settings
echo "<div class='section'>";
echo "<h2>Current Settings Status</h2>";

try {
    $currentSettings = UserNextcloudSettings::getActiveForUser(1);
    
    if ($currentSettings) {
        echo "<p class='success'>✅ Found active settings</p>";
        echo "<p><strong>ID:</strong> " . $currentSettings->id . "</p>";
        echo "<p><strong>Server URL:</strong> " . htmlspecialchars($currentSettings->server_url) . "</p>";
        echo "<p><strong>Username:</strong> " . htmlspecialchars($currentSettings->username) . "</p>";
        echo "<p><strong>Has Password:</strong> " . (!empty($currentSettings->password) ? 'YES' : 'NO') . "</p>";
        
        if (!empty($currentSettings->password)) {
            echo "<p><strong>Password Field Length:</strong> " . strlen($currentSettings->password) . " characters</p>";
            
            $decryptedPassword = $currentSettings->getDecryptedPassword();
            echo "<p><strong>Decryption Status:</strong> " . (!empty($decryptedPassword) ? 'SUCCESS' : 'FAILED') . "</p>";
            
            if (!empty($decryptedPassword)) {
                echo "<p><strong>Decrypted Length:</strong> " . strlen($decryptedPassword) . " characters</p>";
            }
        }
        
        echo "<p><strong>Last Test Result:</strong> " . ($currentSettings->last_test_result ?: 'Never tested') . "</p>";
        if ($currentSettings->last_error_message) {
            echo "<p><strong>Last Error:</strong> " . htmlspecialchars($currentSettings->last_error_message) . "</p>";
        }
        
    } else {
        echo "<p class='warning'>⚠️ No active settings found</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</div>";

// Show forms
if (isset($currentSettings) && $currentSettings) {
    echo "<div class='section'>";
    echo "<h2>Update Password</h2>";
    echo "<p>Enter your correct Nextcloud app password to fix the authentication:</p>";
    
    echo "<form method='POST'>";
    echo "<div class='form-group'>";
    echo "<label for='new_password'>Nextcloud App Password:</label>";
    echo "<input type='password' id='new_password' name='new_password' placeholder='Enter your Nextcloud app password' required>";
    echo "<small>Generate this in Nextcloud: Settings > Security > App passwords</small>";
    echo "</div>";
    echo "<button type='submit' class='btn btn-primary'>Update Password</button>";
    echo "</form>";
    
    echo "<hr>";
    
    echo "<h3>Danger Zone</h3>";
    echo "<p>Delete current settings to start fresh:</p>";
    echo "<form method='POST' onsubmit='return confirm(\"Are you sure you want to delete the settings?\");'>";
    echo "<button type='submit' name='delete_settings' class='btn btn-danger'>Delete Settings</button>";
    echo "</form>";
    
    echo "</div>";
} else {
    echo "<div class='section'>";
    echo "<h2>Create New Settings</h2>";
    echo "<p>No settings found. Create new ones:</p>";
    echo "<a href='/create_test_nextcloud_settings.php' class='btn btn-success'>Create New Settings</a>";
    echo "</div>";
}

echo "<div class='section'>";
echo "<h2>Quick Links</h2>";
echo "<a href='/check_user_nextcloud_settings.php' class='btn btn-primary'>🔍 Check Settings</a>";
echo "<a href='/debug_password_storage.php' class='btn btn-primary'>🔧 Debug Password</a>";
echo "<a href='/test_real_client.php' class='btn btn-primary'>🧪 Test Real Client</a>";
echo "<a href='/admin/user-settings' class='btn btn-primary'>📝 User Settings Page</a>";
echo "</div>";

echo "</body></html>";
