<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Services\NextcloudClient;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

session_start();

$app = Application::getInstance();

if (!$app->isLoggedIn()) {
    echo "<p style='color: red;'>Not logged in! Please <a href='/login'>login first</a></p>";
    exit;
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Nextcloud Configuration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
        .config-section { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
        pre { background: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test Nextcloud Configuration</h1>
    
    <div class="config-section">
        <h2>1. Environment Variables</h2>
        <table border="1" cellpadding="5" cellspacing="0">
            <tr>
                <th>Variable</th>
                <th>Value</th>
                <th>Status</th>
            </tr>
            <tr>
                <td>NEXTCLOUD_URL</td>
                <td><?= htmlspecialchars($_ENV['NEXTCLOUD_URL'] ?? 'Not set') ?></td>
                <td class="<?= !empty($_ENV['NEXTCLOUD_URL']) ? 'success' : 'error' ?>">
                    <?= !empty($_ENV['NEXTCLOUD_URL']) ? '✅' : '❌' ?>
                </td>
            </tr>
            <tr>
                <td>NEXTCLOUD_USERNAME</td>
                <td><?= htmlspecialchars($_ENV['NEXTCLOUD_USERNAME'] ?? 'Not set') ?></td>
                <td class="<?= !empty($_ENV['NEXTCLOUD_USERNAME']) ? 'success' : 'error' ?>">
                    <?= !empty($_ENV['NEXTCLOUD_USERNAME']) ? '✅' : '❌' ?>
                </td>
            </tr>
            <tr>
                <td>NEXTCLOUD_PASSWORD</td>
                <td><?= !empty($_ENV['NEXTCLOUD_PASSWORD']) ? str_repeat('*', strlen($_ENV['NEXTCLOUD_PASSWORD'])) : 'Not set' ?></td>
                <td class="<?= !empty($_ENV['NEXTCLOUD_PASSWORD']) ? 'success' : 'error' ?>">
                    <?= !empty($_ENV['NEXTCLOUD_PASSWORD']) ? '✅' : '❌' ?>
                </td>
            </tr>
            <tr>
                <td>NEXTCLOUD_AUTH_METHOD</td>
                <td><?= htmlspecialchars($_ENV['NEXTCLOUD_AUTH_METHOD'] ?? 'Not set') ?></td>
                <td class="<?= !empty($_ENV['NEXTCLOUD_AUTH_METHOD']) ? 'success' : 'warning' ?>">
                    <?= !empty($_ENV['NEXTCLOUD_AUTH_METHOD']) ? '✅' : '⚠️' ?>
                </td>
            </tr>
        </table>
    </div>
    
    <div class="config-section">
        <h2>2. Configuration File</h2>
        <?php
        try {
            $config = require __DIR__ . '/../config/nextcloud.php';
            echo "<p class='success'>✅ Configuration file loaded successfully</p>";
            echo "<h3>Server Configuration:</h3>";
            echo "<pre>" . print_r($config['server'], true) . "</pre>";
            echo "<h3>Auth Configuration:</h3>";
            $authConfig = $config['auth'];
            $authConfig['password'] = !empty($authConfig['password']) ? str_repeat('*', strlen($authConfig['password'])) : 'Not set';
            echo "<pre>" . print_r($authConfig, true) . "</pre>";
        } catch (Exception $e) {
            echo "<p class='error'>❌ Error loading configuration: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        ?>
    </div>
    
    <div class="config-section">
        <h2>3. Nextcloud Client Test</h2>
        <?php
        try {
            $client = new NextcloudClient();
            echo "<p class='success'>✅ NextcloudClient instantiated successfully</p>";
            
            echo "<h3>Testing Connection:</h3>";
            
            // Test basic connection
            try {
                $items = $client->listDirectory('/');
                echo "<p class='success'>✅ Successfully connected to Nextcloud!</p>";
                echo "<p class='info'>Found " . count($items) . " items in root directory</p>";
                
                if (!empty($items)) {
                    echo "<h4>Root Directory Contents:</h4>";
                    echo "<ul>";
                    foreach (array_slice($items, 0, 10) as $item) {
                        $icon = $item['is_directory'] ? '📁' : '📄';
                        echo "<li>$icon " . htmlspecialchars($item['name']) . 
                             ($item['is_directory'] ? '' : ' (' . formatBytes($item['size']) . ')') . "</li>";
                    }
                    if (count($items) > 10) {
                        echo "<li>... and " . (count($items) - 10) . " more items</li>";
                    }
                    echo "</ul>";
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ Connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
                
                // Provide troubleshooting tips
                echo "<h4>Troubleshooting Tips:</h4>";
                echo "<ul>";
                echo "<li>Check if the Nextcloud URL is correct and accessible</li>";
                echo "<li>Verify username and password/app-password</li>";
                echo "<li>Make sure WebDAV is enabled on your Nextcloud server</li>";
                echo "<li>Check SSL certificate if using HTTPS</li>";
                echo "</ul>";
            }
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Error creating NextcloudClient: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        ?>
    </div>
    
    <div class="config-section">
        <h2>4. Storage Permissions</h2>
        <p>Current user storage permissions:</p>
        <ul>
            <li>storage.view: <?= $app->hasPermission('storage.view') ? '✅' : '❌' ?></li>
            <li>storage.upload: <?= $app->hasPermission('storage.upload') ? '✅' : '❌' ?></li>
            <li>storage.download: <?= $app->hasPermission('storage.download') ? '✅' : '❌' ?></li>
            <li>storage.create: <?= $app->hasPermission('storage.create') ? '✅' : '❌' ?></li>
            <li>storage.edit: <?= $app->hasPermission('storage.edit') ? '✅' : '❌' ?></li>
            <li>storage.delete: <?= $app->hasPermission('storage.delete') ? '✅' : '❌' ?></li>
        </ul>
    </div>
    
    <div class="config-section">
        <h2>5. Next Steps</h2>
        <?php if (empty($_ENV['NEXTCLOUD_URL']) || empty($_ENV['NEXTCLOUD_USERNAME']) || empty($_ENV['NEXTCLOUD_PASSWORD'])): ?>
            <div class="error">
                <h3>⚠️ Configuration Required</h3>
                <p>To use the Storage functionality, you need to configure Nextcloud connection:</p>
                <ol>
                    <li>Edit the <code>.env</code> file</li>
                    <li>Set the following variables:
                        <pre>NEXTCLOUD_URL=https://your-nextcloud-server.com
NEXTCLOUD_USERNAME=your-username
NEXTCLOUD_PASSWORD=your-app-password</pre>
                    </li>
                    <li>Create an App Password in Nextcloud (Settings → Security)</li>
                    <li>Refresh this page to test the connection</li>
                </ol>
            </div>
        <?php else: ?>
            <div class="success">
                <h3>✅ Configuration Complete</h3>
                <p>Your Nextcloud configuration appears to be set up. You can now:</p>
                <ul>
                    <li><a href="/admin/storage">Access the Storage/File Manager</a></li>
                    <li>Upload files to Nextcloud through the ERP system</li>
                    <li>Browse and manage your Nextcloud files</li>
                </ul>
            </div>
        <?php endif; ?>
    </div>
    
    <p><a href="/admin">← Back to Dashboard</a></p>
</body>
</html>

<?php
function formatBytes($size, $precision = 2) {
    if ($size == 0) return '0 B';
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $i = floor(log($size) / log(1024));
    return round($size / pow(1024, $i), $precision) . ' ' . $units[$i];
}
?>
