<?php
/**
 * Test script for StrixBudget models
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$envFile = __DIR__ . '/../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        [$name, $value] = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

use Strix\ERP\Services\StrixBudgetClient;
use Strix\ERP\Models\StrixBudget\BaseStrixBudgetModel;
use Strix\ERP\Models\StrixBudget\Transaction;
use Strix\ERP\Models\StrixBudget\BankAccount;

?>
<!DOCTYPE html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StrixBudget Models Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>🧪 StrixBudget Models Test</h1>
        <p class="text-muted">Тестване на StrixBudget модели и API интеграция</p>

        <?php
        echo "<h2>1. Client Initialization</h2>";
        try {
            $apiUrl = $_ENV['STRIXBUDGET_API_URL'] ?? 'http://localhost:8000/api';
            $client = new StrixBudgetClient($apiUrl);
            echo "<p class='success'>✅ StrixBudgetClient created successfully</p>";
            echo "<p><strong>API URL:</strong> <code>$apiUrl</code></p>";
            echo "<p><strong>Mock Mode:</strong> " . ($client->isMockMode() ? 'Yes' : 'No') . "</p>";
            
            // Set client for models
            BaseStrixBudgetModel::setClient($client);
            echo "<p class='success'>✅ Client set for models</p>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Error creating client: " . $e->getMessage() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }

        echo "<h2>2. Model Constructor Tests</h2>";
        
        // Test different constructor inputs
        $testCases = [
            'Empty array' => [],
            'Valid data array' => [
                'id' => 1,
                'amount' => 100.50,
                'description' => 'Test transaction',
                'type' => 'expense'
            ],
            'Integer (should not crash)' => 123,
            'String (should not crash)' => 'test',
            'Object' => (object) ['id' => 2, 'amount' => 200.00]
        ];

        foreach ($testCases as $testName => $testData) {
            echo "<h3>Testing: $testName</h3>";
            try {
                $transaction = new Transaction($testData);
                echo "<p class='success'>✅ Transaction created successfully</p>";
                echo "<p><strong>Attributes:</strong> <code>" . json_encode($transaction->getAttributes()) . "</code></p>";
            } catch (Exception $e) {
                echo "<p class='error'>❌ Error creating transaction: " . $e->getMessage() . "</p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
            }
        }

        echo "<h2>3. API Data Retrieval Test</h2>";
        if (isset($client)) {
            try {
                echo "<h3>Testing Transaction::getFiltered()</h3>";
                $transactions = Transaction::getFiltered(['per_page' => 5]);
                echo "<p class='success'>✅ Retrieved " . count($transactions) . " transactions</p>";
                
                if (!empty($transactions)) {
                    echo "<h4>Sample Transaction:</h4>";
                    $firstTransaction = $transactions[0];
                    echo "<pre>" . json_encode($firstTransaction->getAttributes(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                    
                    echo "<h4>Transaction Methods:</h4>";
                    echo "<p><strong>Formatted Amount:</strong> " . $firstTransaction->getFormattedAmount() . "</p>";
                    echo "<p><strong>Is Income:</strong> " . ($firstTransaction->isIncome() ? 'Yes' : 'No') . "</p>";
                    echo "<p><strong>Type Icon:</strong> " . $firstTransaction->getTypeIcon() . "</p>";
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ Error getting transactions: " . $e->getMessage() . "</p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
            }

            try {
                echo "<h3>Testing BankAccount::all()</h3>";
                $accounts = BankAccount::all();
                echo "<p class='success'>✅ Retrieved " . count($accounts) . " bank accounts</p>";
                
                if (!empty($accounts)) {
                    echo "<h4>Sample Bank Account:</h4>";
                    $firstAccount = $accounts[0];
                    echo "<pre>" . json_encode($firstAccount->getAttributes(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ Error getting bank accounts: " . $e->getMessage() . "</p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
            }
        }

        echo "<h2>4. Mock Data Structure Test</h2>";
        if (isset($client)) {
            try {
                // Force mock mode for testing
                $client->setMockMode(true);
                echo "<p class='info'>🔧 Forced mock mode for testing</p>";
                
                $response = $client->getTransactions(['per_page' => 2]);
                echo "<h4>Raw API Response:</h4>";
                echo "<pre>" . json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                
                if ($response['success'] && isset($response['data'])) {
                    echo "<h4>Data Structure Analysis:</h4>";
                    foreach ($response['data'] as $index => $item) {
                        echo "<p><strong>Item $index type:</strong> " . gettype($item) . "</p>";
                        if (is_array($item)) {
                            echo "<p><strong>Item $index keys:</strong> " . implode(', ', array_keys($item)) . "</p>";
                        } else {
                            echo "<p><strong>Item $index value:</strong> " . print_r($item, true) . "</p>";
                        }
                    }
                }
                
            } catch (Exception $e) {
                echo "<p class='error'>❌ Error testing mock data: " . $e->getMessage() . "</p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
            }
        }

        echo "<h2>5. Error Handling Test</h2>";
        try {
            // Test with invalid client
            $invalidClient = new StrixBudgetClient('http://invalid-url');
            $invalidClient->setMockMode(false);
            BaseStrixBudgetModel::setClient($invalidClient);
            
            echo "<p class='info'>🔧 Testing with invalid API URL</p>";
            $transactions = Transaction::getFiltered(['per_page' => 1]);
            echo "<p class='success'>✅ Error handling worked, got " . count($transactions) . " transactions (should be 0)</p>";
            
        } catch (Exception $e) {
            echo "<p class='warning'>⚠️ Expected error caught: " . $e->getMessage() . "</p>";
        }
        ?>

        <div class="mt-5">
            <h2>6. Summary</h2>
            <div class="alert alert-info">
                <h5>Test Results:</h5>
                <ul>
                    <li>Model constructors should handle different input types gracefully</li>
                    <li>API data retrieval should work with both mock and real data</li>
                    <li>Error handling should prevent crashes</li>
                    <li>Data structures should be consistent</li>
                </ul>
            </div>
        </div>

        <div class="mt-3">
            <a href="/admin/strixbudget" class="btn btn-primary">🏠 StrixBudget Dashboard</a>
            <a href="/test_strixbudget_api.php" class="btn btn-secondary">🔧 API Test</a>
            <a href="/strixbudget_api_demo.html" class="btn btn-success">🚀 API Demo</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
