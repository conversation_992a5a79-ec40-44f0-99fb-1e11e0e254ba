<?php

return [
    // Nextcloud server configuration
    'server' => [
        'url' => $_ENV['NEXTCLOUD_URL'] ?? 'https://your-nextcloud-server.com',
        'webdav_path' => '/remote.php/dav/files/',
        'timeout' => 30,
        'verify_ssl' => $_ENV['NEXTCLOUD_VERIFY_SSL'] ?? true,
    ],
    
    // Authentication
    'auth' => [
        // Authentication method: 'basic' or 'app_password'
        'method' => $_ENV['NEXTCLOUD_AUTH_METHOD'] ?? 'app_password',
        
        // For basic auth or app password
        'username' => $_ENV['NEXTCLOUD_USERNAME'] ?? '',
        'password' => $_ENV['NEXTCLOUD_PASSWORD'] ?? '',
        
        // For OAuth (future implementation)
        'client_id' => $_ENV['NEXTCLOUD_CLIENT_ID'] ?? '',
        'client_secret' => $_ENV['NEXTCLOUD_CLIENT_SECRET'] ?? '',
    ],
    
    // File handling
    'files' => [
        // Maximum file size for upload (in bytes)
        'max_upload_size' => $_ENV['NEXTCLOUD_MAX_UPLOAD_SIZE'] ?? 100 * 1024 * 1024, // 100MB
        
        // Allowed file extensions
        'allowed_extensions' => [
            // Documents
            'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'odt', 'ods', 'odp',
            // Images
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp',
            // Videos
            'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm',
            // Audio
            'mp3', 'wav', 'ogg', 'flac', 'aac',
            // Archives
            'zip', 'rar', '7z', 'tar', 'gz',
            // Text
            'txt', 'md', 'csv', 'json', 'xml', 'html', 'css', 'js',
            // Other
            'rtf', 'epub', 'mobi'
        ],
        
        // Blocked file extensions for security
        'blocked_extensions' => [
            'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'php', 'asp', 'jsp'
        ],
        
        // Default folder for uploads
        'default_upload_folder' => '/ERP_Uploads',
        
        // Thumbnail settings
        'thumbnails' => [
            'enabled' => true,
            'width' => 150,
            'height' => 150,
            'quality' => 80
        ]
    ],
    
    // Caching
    'cache' => [
        'enabled' => true,
        'ttl' => 300, // 5 minutes
        'prefix' => 'nextcloud_'
    ],
    
    // Logging
    'logging' => [
        'enabled' => $_ENV['NEXTCLOUD_LOGGING'] ?? true,
        'level' => $_ENV['NEXTCLOUD_LOG_LEVEL'] ?? 'debug', // debug, info, warning, error
    ],
    
    // Features
    'features' => [
        'sharing' => true,
        'versioning' => true,
        'comments' => true,
        'tags' => true,
        'favorites' => true,
        'trash' => true
    ],
    
    // UI Settings
    'ui' => [
        'items_per_page' => 50,
        'show_hidden_files' => false,
        'default_view' => 'list', // list, grid
        'sort_by' => 'name', // name, size, modified
        'sort_order' => 'asc' // asc, desc
    ]
];
