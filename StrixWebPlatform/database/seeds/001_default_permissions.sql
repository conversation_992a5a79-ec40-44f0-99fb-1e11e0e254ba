-- Insert default permissions
INSERT INTO permissions (name, description, module, action) VALUES
-- User management permissions
('users.view', 'Преглед на потребители', 'users', 'view'),
('users.create', 'Създаване на потребители', 'users', 'create'),
('users.edit', 'Редактиране на потребители', 'users', 'edit'),
('users.delete', 'Изтриване на потребители', 'users', 'delete'),
('users.manage_groups', 'Управление на групи на потребители', 'users', 'manage_groups'),

-- Group management permissions
('groups.view', 'Преглед на групи', 'groups', 'view'),
('groups.create', 'Създаване на групи', 'groups', 'create'),
('groups.edit', 'Редактиране на групи', 'groups', 'edit'),
('groups.delete', 'Изтриване на групи', 'groups', 'delete'),
('groups.manage_permissions', 'Управление на права на групи', 'groups', 'manage_permissions'),

-- Admin dashboard permissions
('admin.dashboard', 'Достъп до администраторски дашборд', 'admin', 'dashboard'),
('admin.system_settings', 'Управление на системни настройки', 'admin', 'system_settings'),
('admin.view_logs', 'Преглед на системни логове', 'admin', 'view_logs'),

-- Reports permissions
('reports.view', 'Преглед на отчети', 'reports', 'view'),
('reports.export', 'Експорт на отчети', 'reports', 'export'),

-- System permissions
('system.backup', 'Създаване на резервни копия', 'system', 'backup'),
('system.restore', 'Възстановяване от резервни копия', 'system', 'restore'),
('system.maintenance', 'Поддръжка на системата', 'system', 'maintenance')

ON DUPLICATE KEY UPDATE 
description = VALUES(description),
module = VALUES(module),
action = VALUES(action);
