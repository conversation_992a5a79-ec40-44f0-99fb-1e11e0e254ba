-- StrixBudget module permissions

-- Insert StrixBudget permissions
INSERT INTO permissions (name, description, module, action, created_at) VALUES
('strixbudget.view', 'Преглед на StrixBudget данни', 'strixbudget', 'view', NOW()),
('strixbudget.manage', 'Управление на StrixBudget данни', 'strixbudget', 'manage', NOW()),
('strixbudget.settings', 'Управление на StrixBudget настройки', 'strixbudget', 'settings', NOW());

-- Grant permissions to administrators group
INSERT INTO group_permissions (group_id, permission_id, granted_at)
SELECT g.id, p.id, NOW()
FROM groups g, permissions p
WHERE g.name = 'administrators'
AND p.module = 'strixbudget';

-- Grant view permission to managers group
INSERT INTO group_permissions (group_id, permission_id, granted_at)
SELECT g.id, p.id, NOW()
FROM groups g, permissions p
WHERE g.name = 'managers'
AND p.name = 'strixbudget.view';
