-- Assign all permissions to administrators group
INSERT INTO group_permissions (group_id, permission_id, granted_by)
SELECT 
    (SELECT id FROM groups WHERE name = 'administrators') as group_id,
    p.id as permission_id,
    (SELECT id FROM users WHERE username = 'admin') as granted_by
FROM permissions p
ON DUPLICATE KEY UPDATE granted_by = VALUES(granted_by);

-- Assign admin user to administrators group
INSERT INTO user_groups (user_id, group_id, assigned_by)
SELECT 
    (SELECT id FROM users WHERE username = 'admin') as user_id,
    (SELECT id FROM groups WHERE name = 'administrators') as group_id,
    (SELECT id FROM users WHERE username = 'admin') as assigned_by
ON DUPLICATE KEY UPDATE assigned_by = VALUES(assigned_by);

-- Assign basic permissions to managers group
INSERT INTO group_permissions (group_id, permission_id, granted_by)
SELECT 
    (SELECT id FROM groups WHERE name = 'managers') as group_id,
    p.id as permission_id,
    (SELECT id FROM users WHERE username = 'admin') as granted_by
FROM permissions p
WHERE p.name IN (
    'users.view', 'users.create', 'users.edit',
    'groups.view',
    'admin.dashboard',
    'reports.view', 'reports.export'
)
ON DUPLICATE KEY UPDATE granted_by = VALUES(granted_by);

-- Assign basic permissions to users group
INSERT INTO group_permissions (group_id, permission_id, granted_by)
SELECT
    (SELECT id FROM groups WHERE name = 'users') as group_id,
    p.id as permission_id,
    (SELECT id FROM users WHERE username = 'admin') as granted_by
FROM permissions p
WHERE p.name IN (
    'reports.view',
    'tasks.view',
    'tasks.comment'
)
ON DUPLICATE KEY UPDATE granted_by = VALUES(granted_by);
