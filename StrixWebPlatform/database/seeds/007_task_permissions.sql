-- Insert task management permissions
INSERT INTO permissions (name, description, module, action) VALUES
-- Task permissions
('tasks.view', 'Преглед на задачи', 'tasks', 'view'),
('tasks.view_all', 'Преглед на всички задачи в системата', 'tasks', 'view_all'),
('tasks.create', 'Създаване на нови задачи', 'tasks', 'create'),
('tasks.edit', 'Редактиране на задачи', 'tasks', 'edit'),
('tasks.edit_all', 'Редактиране на всички задачи', 'tasks', 'edit_all'),
('tasks.delete', 'Изтриване на задачи', 'tasks', 'delete'),
('tasks.assign', 'Назначаване на изпълнители на задачи', 'tasks', 'assign'),
('tasks.comment', 'Добавя<PERSON>е на коментари към задачи', 'tasks', 'comment'),
('tasks.attach', 'Прикачване на файлове към задачи', 'tasks', 'attach'),
('tasks.change_status', 'Промяна на статуса на задачи', 'tasks', 'change_status'),
('tasks.view_reports', 'Преглед на отчети за задачи', 'tasks', 'view_reports'),

-- Task type permissions
('task_types.view', 'Преглед на типове задачи', 'task_types', 'view'),
('task_types.manage', 'Управление на типове задачи', 'task_types', 'manage'),

-- Task status permissions
('task_statuses.view', 'Преглед на статуси на задачи', 'task_statuses', 'view'),
('task_statuses.manage', 'Управление на статуси на задачи', 'task_statuses', 'manage')

ON DUPLICATE KEY UPDATE 
description = VALUES(description),
module = VALUES(module),
action = VALUES(action);
