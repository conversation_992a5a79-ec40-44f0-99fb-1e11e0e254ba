<?php

/**
 * Test session and authentication
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Strix\ERP\Core\Application;
use Strix\ERP\Models\User;

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

try {
    echo "🔍 Тестване на сесията и автентикацията...\n\n";
    
    // Initialize application
    $app = Application::getInstance();
    
    echo "1. Проверка на Application instance...\n";
    echo "   ✓ Application е инициализиран\n\n";
    
    // Check if user is logged in
    echo "2. Проверка на автентикацията...\n";
    $isLoggedIn = $app->isLoggedIn();
    echo "   Logged in: " . ($isLoggedIn ? 'Да' : 'Не') . "\n";
    
    if ($isLoggedIn) {
        $currentUser = $app->getCurrentUser();
        echo "   Потребител: " . $currentUser['full_name'] . "\n";
        echo "   Username: " . $currentUser['username'] . "\n";
        echo "   Email: " . $currentUser['email'] . "\n";
        echo "   Is Admin: " . ($currentUser['is_admin'] ? 'Да' : 'Не') . "\n";
        echo "   Права: " . count($currentUser['permissions']) . "\n";
        
        // Check specific task permissions
        echo "\n3. Проверка на права за задачи...\n";
        $taskPermissions = ['tasks.view', 'tasks.create', 'tasks.edit', 'tasks.delete'];
        foreach ($taskPermissions as $permission) {
            $hasPermission = $app->hasPermission($permission);
            echo "   $permission: " . ($hasPermission ? '✓' : '❌') . "\n";
        }
    } else {
        echo "   ❌ Потребителят не е влязъл в системата\n";
        
        // Try to simulate login
        echo "\n3. Опит за автоматично влизане...\n";
        $admin = User::findByUsername('admin');
        if ($admin) {
            $permissions = $admin->getPermissionNames();
            
            $app->setCurrentUser([
                'id' => $admin->id,
                'username' => $admin->username,
                'email' => $admin->email,
                'first_name' => $admin->first_name,
                'last_name' => $admin->last_name,
                'full_name' => $admin->getFullName(),
                'permissions' => $permissions,
                'is_admin' => $admin->hasGroup('administrators')
            ]);
            
            echo "   ✓ Потребителят е влязъл автоматично\n";
            echo "   Права: " . count($permissions) . "\n";
            
            // Check task permissions again
            echo "\n4. Проверка на права за задачи след влизане...\n";
            $taskPermissions = ['tasks.view', 'tasks.create', 'tasks.edit', 'tasks.delete'];
            foreach ($taskPermissions as $permission) {
                $hasPermission = $app->hasPermission($permission);
                echo "   $permission: " . ($hasPermission ? '✓' : '❌') . "\n";
            }
        } else {
            echo "   ❌ Администраторът не е намерен\n";
        }
    }
    
    echo "\n🎉 Тестването завърши!\n";
    
} catch (Exception $e) {
    echo "❌ Грешка: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
