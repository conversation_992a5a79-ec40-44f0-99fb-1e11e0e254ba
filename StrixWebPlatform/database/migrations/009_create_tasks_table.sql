-- Create tasks table
CREATE TABLE IF NOT EXISTS tasks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    task_type_id INT NOT NULL,
    status_id INT NOT NULL,
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    progress INT DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    
    -- Dates
    due_date DATE NULL,
    start_date DATE NULL,
    completed_at TIMESTAMP NULL,
    
    -- User relationships
    created_by INT NOT NULL,
    assigned_to INT NULL, -- Primary assignee
    
    -- Additional fields
    estimated_hours DECIMAL(8,2) NULL,
    actual_hours DECIMAL(8,2) NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (task_type_id) REFERENCES task_types(id) ON DELETE RESTRICT,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (status_id) REFERENCES task_statuses(id) ON DELETE RESTRICT,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_title (title),
    INDEX idx_status (status_id),
    INDEX idx_type (task_type_id),
    INDEX idx_priority (priority),
    INDEX idx_progress (progress),
    INDEX idx_due_date (due_date),
    INDEX idx_created_by (created_by),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
