-- Migration: Create user_strixbudget_settings table
-- Description: Table for storing user-specific StrixBudget API settings

CREATE TABLE IF NOT EXISTS user_strixbudget_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    api_url VARCHAR(255) NOT NULL,
    api_token TEXT,
    is_active BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_settings (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add index for faster lookups
CREATE INDEX idx_user_strixbudget_settings_user_id ON user_strixbudget_settings(user_id);
CREATE INDEX idx_user_strixbudget_settings_active ON user_strixbudget_settings(is_active);
