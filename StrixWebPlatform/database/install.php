<?php

/**
 * Database Installation Script
 * This script creates the database schema and inserts initial data
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $_ENV[trim($name)] = trim($value);
    }
}

$config = require __DIR__ . '/../config/database.php';

try {
    // Connect to MySQL server (without database)
    $dsn = "mysql:host={$config['host']};port={$config['port']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    
    echo "Свързване с MySQL сървъра... ✓\n";
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "Създаване на база данни '{$config['database']}'... ✓\n";
    
    // Connect to the specific database
    $dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    
    echo "Свързване с база данни '{$config['database']}'... ✓\n";
    
    // Run migrations
    $migrationFiles = glob(__DIR__ . '/migrations/*.sql');
    sort($migrationFiles);
    
    foreach ($migrationFiles as $file) {
        $sql = file_get_contents($file);
        $pdo->exec($sql);
        echo "Изпълнение на миграция: " . basename($file) . "... ✓\n";
    }
    
    // Run seeds
    $seedFiles = glob(__DIR__ . '/seeds/*.sql');
    sort($seedFiles);
    
    foreach ($seedFiles as $file) {
        $sql = file_get_contents($file);
        $pdo->exec($sql);
        echo "Изпълнение на seed: " . basename($file) . "... ✓\n";
    }
    
    echo "\n🎉 Базата данни е успешно инсталирана!\n";
    echo "Можете да се логнете с:\n";
    echo "Потребителско име: admin\n";
    echo "Парола: admin123\n\n";
    
} catch (PDOException $e) {
    echo "❌ Грешка при инсталация на базата данни: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Неочаквана грешка: " . $e->getMessage() . "\n";
    exit(1);
}
