<?php

namespace Strix\ERP\Controllers\Api;

use Strix\ERP\Core\Controller;
use Strix\ERP\Services\StrixBudgetClient;
use Strix\ERP\Models\UserStrixBudgetSettings;

class StrixBudgetApiController extends Controller
{
    private ?StrixBudgetClient $client = null;
    private ?UserStrixBudgetSettings $settings = null;
    
    public function __construct()
    {
        parent::__construct();
        $this->initializeClient();
    }

    /**
     * Get JSON input from request body
     */
    private function getJsonInput(): array
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        return $data ?? [];
    }

    /**
     * Get StrixBudget client with Bearer token support
     */
    private function getClientWithToken(): ?StrixBudgetClient
    {
        // Check for Bearer token in Authorization header
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $token = $matches[1];
            $apiUrl = $_ENV['STRIXBUDGET_API_URL'] ?? 'http://localhost:8000/api';
            $client = new StrixBudgetClient($apiUrl);
            $client->setToken($token);
            return $client;
        }

        // Fallback to configured client
        return $this->client;
    }
    
    /**
     * Initialize StrixBudget client with user settings
     */
    private function initializeClient(): void
    {
        $this->requireAuth();

        $currentUser = $this->app->getCurrentUser();
        $this->settings = UserStrixBudgetSettings::getByUserId($currentUser['id']);

        if ($this->settings && $this->settings->isConfigured()) {
            $config = $this->settings->getClientConfig();
            $this->client = new StrixBudgetClient($config['api_url']);

            // Authenticate based on method
            $authSuccess = false;
            if ($this->settings->usesCredentials()) {
                try {
                    $loginResult = $this->client->loginAndGetToken($config['api_email'], $config['api_password']);
                    if ($loginResult['success']) {
                        $this->client->setToken($loginResult['token']);
                        $authSuccess = true;
                    }
                } catch (\Exception $e) {
                    error_log("StrixBudget API login exception: " . $e->getMessage());
                }
            } else {
                if (!empty($config['api_token'])) {
                    $this->client->setToken($config['api_token']);
                    $authSuccess = true;
                }
            }

            if (!$authSuccess) {
                $this->client = null;
            }
        }
    }

    /**
     * Login to StrixBudget API and return token
     * POST /api/strixbudget/auth/login
     */
    public function login(): void
    {
        $this->requireAuth();
        
        $data = $this->getJsonInput();
        
        if (empty($data['email']) || empty($data['password'])) {
            $this->json([
                'success' => false,
                'message' => 'Email и парола са задължителни'
            ], 400);
            return;
        }

        try {
            $apiUrl = $data['api_url'] ?? $_ENV['STRIXBUDGET_API_URL'] ?? 'http://localhost:8000/api';
            $client = new StrixBudgetClient($apiUrl);
            
            $loginResult = $client->loginAndGetToken($data['email'], $data['password']);
            
            if ($loginResult['success']) {
                // Optionally save credentials for this user
                if (isset($data['save_credentials']) && $data['save_credentials']) {
                    $currentUser = $this->app->getCurrentUser();
                    $settings = UserStrixBudgetSettings::getOrCreateForUser($currentUser['id']);
                    $settings->api_url = $apiUrl;
                    $settings->auth_method = 'credentials';
                    $settings->setEncryptedEmail($data['email']);
                    $settings->setEncryptedPassword($data['password']);
                    $settings->setEncryptedToken($loginResult['token']);
                    $settings->is_active = true;
                    $settings->save();
                }
                
                $this->json([
                    'success' => true,
                    'data' => [
                        'token' => $loginResult['token'],
                        'user' => $loginResult['user'] ?? null,
                        'expires_in' => 3600 // 1 hour default
                    ],
                    'message' => 'Успешно влизане в StrixBudget'
                ]);
            } else {
                $this->json([
                    'success' => false,
                    'message' => $loginResult['message'] ?? 'Грешка при влизане'
                ], 401);
            }
        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Грешка при свързване с API: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current user info from StrixBudget API
     * GET /api/strixbudget/auth/user
     */
    public function getCurrentUser(): void
    {
        $this->requireAuth();

        $client = $this->getClientWithToken();
        if (!$client) {
            $this->json([
                'success' => false,
                'message' => 'StrixBudget не е конфигуриран и няма Bearer token'
            ], 400);
            return;
        }

        try {
            $result = $client->getCurrentUser();
            $this->json($result);
        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Грешка при получаване на потребителска информация: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get bank accounts
     * GET /api/strixbudget/bank-accounts
     */
    public function getBankAccounts(): void
    {
        $this->requireAuth();

        $client = $this->getClientWithToken();
        if (!$client) {
            $this->json([
                'success' => false,
                'message' => 'StrixBudget не е конфигуриран'
            ], 400);
            return;
        }

        try {
            $result = $client->getBankAccounts();
            $this->json($result);
        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Грешка при получаване на банкови сметки: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get transactions with optional filters
     * GET /api/strixbudget/transactions
     */
    public function getTransactions(): void
    {
        $this->requireAuth();

        $client = $this->getClientWithToken();
        if (!$client) {
            $this->json([
                'success' => false,
                'message' => 'StrixBudget не е конфигуриран'
            ], 400);
            return;
        }

        try {
            $filters = $_GET;
            unset($filters['_']); // Remove cache buster

            $result = $client->getTransactions($filters);
            $this->json($result);
        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Грешка при получаване на транзакции: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new transaction
     * POST /api/strixbudget/transactions
     */
    public function createTransaction(): void
    {
        $this->requireAuth();

        $client = $this->getClientWithToken();
        if (!$client) {
            $this->json([
                'success' => false,
                'message' => 'StrixBudget не е конфигуриран'
            ], 400);
            return;
        }

        try {
            $data = $this->getJsonInput();
            $result = $client->createTransaction($data);
            $this->json($result);
        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Грешка при създаване на транзакция: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get counterparties
     * GET /api/strixbudget/counterparties
     */
    public function getCounterparties(): void
    {
        $this->requireAuth();

        $client = $this->getClientWithToken();
        if (!$client) {
            $this->json([
                'success' => false,
                'message' => 'StrixBudget не е конфигуриран'
            ], 400);
            return;
        }

        try {
            $filters = $_GET;
            unset($filters['_']);

            $result = $client->getCounterparties($filters);
            $this->json($result);
        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Грешка при получаване на контрагенти: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user statistics
     * GET /api/strixbudget/statistics
     */
    public function getStatistics(): void
    {
        $this->requireAuth();

        $client = $this->getClientWithToken();
        if (!$client) {
            $this->json([
                'success' => false,
                'message' => 'StrixBudget не е конфигуриран'
            ], 400);
            return;
        }

        try {
            $result = $client->getUserStatistics();
            $this->json($result);
        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Грешка при получаване на статистики: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test API connection
     * POST /api/strixbudget/test-connection
     */
    public function testConnection(): void
    {
        $this->requireAuth();
        
        $data = $this->getJsonInput();
        
        try {
            $apiUrl = $data['api_url'] ?? $_ENV['STRIXBUDGET_API_URL'] ?? 'http://localhost:8000/api';
            $client = new StrixBudgetClient($apiUrl);
            
            if (isset($data['token'])) {
                $client->setToken($data['token']);
            } elseif (isset($data['email']) && isset($data['password'])) {
                $loginResult = $client->loginAndGetToken($data['email'], $data['password']);
                if (!$loginResult['success']) {
                    $this->json($loginResult, 401);
                    return;
                }
                $client->setToken($loginResult['token']);
            }
            
            $result = $client->testConnection();
            $this->json($result);
        } catch (\Exception $e) {
            $this->json([
                'success' => false,
                'message' => 'Грешка при тестване на връзката: ' . $e->getMessage()
            ], 500);
        }
    }
}
