<?php

namespace Strix\ERP\Controllers;

use Strix\ERP\Core\Controller;
use Strix\ERP\Core\Database;
use Strix\ERP\Models\User;
use Strix\ERP\Models\Group;
use Strix\ERP\Models\Permission;
use Strix\ERP\Models\Task;
use Strix\ERP\Models\TaskStatus;

class AdminController extends Controller
{
    public function dashboard(): void
    {
        $this->requireAuth();
        $this->requirePermission('admin.dashboard');
        
        // Get dashboard statistics
        $stats = $this->getDashboardStats();
        
        // Get recent activities (you can implement this later)
        $recentActivities = $this->getRecentActivities();
        
        $this->view('admin/dashboard', [
            'title' => 'Администраторски дашборд',
            'showSidebar' => true,
            'stats' => $stats,
            'recent_activities' => $recentActivities
        ]);
    }
    
    private function getDashboardStats(): array
    {
        $stats = [
            'total_users' => User::count(),
            'active_users' => count(User::getActiveUsers()),
            'total_groups' => Group::count(),
            'active_groups' => count(Group::getActiveGroups()),
            'total_permissions' => Permission::count(),
            'total_modules' => count(Permission::getAllModules())
        ];

        // Add task statistics if user has permission
        if ($this->app->hasPermission('tasks.view')) {
            $stats['total_tasks'] = Task::count();
            $stats['overdue_tasks'] = count(Task::getOverdueTasks());

            // Get tasks by status (optimized query)
            $completedStatus = TaskStatus::findByName('Завършена');
            if ($completedStatus) {
                $stats['completed_tasks'] = count(Task::getTasksByStatus($completedStatus->id));
            }

            $inProgressStatus = TaskStatus::findByName('В прогрес');
            if ($inProgressStatus) {
                $stats['in_progress_tasks'] = count(Task::getTasksByStatus($inProgressStatus->id));
            }
        }

        return $stats;
    }
    
    private function getRecentActivities(): array
    {
        // For now, return some sample data
        // Later you can implement a proper activity log system
        return [
            [
                'user' => 'Администратор Системен',
                'action' => 'Създаде нов потребител',
                'target' => 'Иван Петров',
                'time' => '2 часа назад'
            ],
            [
                'user' => 'Администратор Системен',
                'action' => 'Редактира група',
                'target' => 'Мениджъри',
                'time' => '5 часа назад'
            ],
            [
                'user' => 'Администратор Системен',
                'action' => 'Влезе в системата',
                'target' => '',
                'time' => '1 ден назад'
            ]
        ];
    }
}
