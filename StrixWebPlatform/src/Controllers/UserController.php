<?php

namespace Strix\ERP\Controllers;

use Strix\ERP\Core\Controller;
use Strix\ERP\Models\User;
use Strix\ERP\Models\Group;

class UserController extends Controller
{
    public function index(): void
    {
        $this->requireAuth();
        $this->requirePermission('users.view');

        $search = $this->getInput('search', '');
        $page = max(1, (int) $this->getInput('page', 1));
        $perPage = 20;

        // Build optimized query with proper pagination
        $whereClause = '';
        $params = [];

        if ($search) {
            $whereClause = "WHERE (first_name LIKE ? OR last_name LIKE ? OR username LIKE ? OR email LIKE ?)";
            $searchTerm = "%$search%";
            $params = [$searchTerm, $searchTerm, $searchTerm, $searchTerm];
        }

        // Get total count efficiently
        $totalUsers = (int) \Strix\ERP\Core\Database::fetchColumn(
            "SELECT COUNT(*) FROM users $whereClause",
            $params
        );

        // Get paginated data
        $offset = ($page - 1) * $perPage;
        $results = \Strix\ERP\Core\Database::fetchAll(
            "SELECT * FROM users $whereClause ORDER BY created_at DESC LIMIT $perPage OFFSET $offset",
            $params
        );

        // Convert to User objects
        $users = [];
        foreach ($results as $userData) {
            $user = new User();
            foreach ($userData as $key => $value) {
                $user->$key = $value;
            }
            $users[] = $user;
        }

        $totalPages = ceil($totalUsers / $perPage);

        $this->view('admin/users/index', [
            'title' => 'Управление на потребители',
            'showSidebar' => true,
            'users' => $users,
            'search' => $search,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalUsers' => $totalUsers
        ]);
    }
    
    public function create(): void
    {
        $this->requireAuth();
        $this->requirePermission('users.create');
        
        $groups = Group::getActiveGroups();
        
        $this->view('admin/users/create', [
            'title' => 'Създаване на потребител',
            'showSidebar' => true,
            'groups' => $groups,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }
    
    public function store(): void
    {
        $this->requireAuth();
        $this->requirePermission('users.create');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/users/create', 'error', 'Невалидна заявка');
        }
        
        $data = $this->getAllInput();
        
        // Validate input
        $errors = $this->validate([
            'username' => 'required|min:3|max:50|unique:users,username',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:8',
            'password_confirm' => 'required',
            'first_name' => 'required|max:50',
            'last_name' => 'required|max:50'
        ]);
        
        // Check password confirmation
        if ($data['password'] !== $data['password_confirm']) {
            $errors['password_confirm'][] = 'Паролите не съвпадат';
        }
        
        if (!empty($errors)) {
            $groups = Group::getActiveGroups();
            $this->view('admin/users/create', [
                'title' => 'Създаване на потребител',
                'showSidebar' => true,
                'groups' => $groups,
                'errors' => $errors,
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }
        
        try {
            // Create user
            $user = new User();
            $user->username = $data['username'];
            $user->email = $data['email'];
            $user->setPassword($data['password']);
            $user->first_name = $data['first_name'];
            $user->last_name = $data['last_name'];
            $user->is_active = isset($data['is_active']) ? 1 : 0;
            
            if ($user->save()) {
                // Assign groups efficiently with batch insert
                if (isset($data['groups']) && is_array($data['groups']) && !empty($data['groups'])) {
                    $currentUser = $this->app->getCurrentUser();
                    $values = [];
                    $placeholders = [];

                    foreach ($data['groups'] as $groupId) {
                        $values[] = $user->id;
                        $values[] = (int) $groupId;
                        $values[] = $currentUser['id'];
                        $values[] = date('Y-m-d H:i:s');
                        $placeholders[] = "(?, ?, ?, ?)";
                    }

                    \Strix\ERP\Core\Database::query(
                        "INSERT INTO user_groups (user_id, group_id, assigned_by, assigned_at) VALUES " .
                        implode(", ", $placeholders),
                        $values
                    );
                }

                $this->redirectWithMessage('/admin/users', 'success', 'Потребителят е създаден успешно');
            } else {
                throw new \Exception('Грешка при запазване на потребителя');
            }
        } catch (\Exception $e) {
            $groups = Group::getActiveGroups();
            $this->view('admin/users/create', [
                'title' => 'Създаване на потребител',
                'showSidebar' => true,
                'groups' => $groups,
                'error' => 'Грешка при създаване на потребителя: ' . $e->getMessage(),
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        }
    }
    
    public function edit(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('users.edit');
        
        $user = User::find($id);
        if (!$user) {
            $this->redirectWithMessage('/admin/users', 'error', 'Потребителят не е намерен');
        }
        
        $groups = Group::getActiveGroups();
        $userGroups = $user->getGroups();
        $userGroupIds = array_map(fn($group) => $group->id, $userGroups);
        
        $this->view('admin/users/edit', [
            'title' => 'Редактиране на потребител',
            'showSidebar' => true,
            'user' => $user,
            'groups' => $groups,
            'userGroupIds' => $userGroupIds,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }
    
    public function update(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('users.edit');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/users', 'error', 'Невалидна заявка');
        }
        
        $user = User::find($id);
        if (!$user) {
            $this->redirectWithMessage('/admin/users', 'error', 'Потребителят не е намерен');
        }
        
        $data = $this->getAllInput();
        
        // Validate input
        $errors = $this->validate([
            'username' => 'required|min:3|max:50',
            'email' => 'required|email',
            'first_name' => 'required|max:50',
            'last_name' => 'required|max:50'
        ]);
        
        // Check for unique username (excluding current user)
        $existingUser = User::findByUsername($data['username']);
        if ($existingUser && $existingUser->id !== $user->id) {
            $errors['username'][] = 'Потребителското име вече съществува';
        }
        
        // Check for unique email (excluding current user)
        $existingUser = User::findByEmail($data['email']);
        if ($existingUser && $existingUser->id !== $user->id) {
            $errors['email'][] = 'Имейлът вече съществува';
        }
        
        // Validate password if provided
        if (!empty($data['password'])) {
            if (strlen($data['password']) < 8) {
                $errors['password'][] = 'Паролата трябва да е поне 8 символа';
            }
            if ($data['password'] !== $data['password_confirm']) {
                $errors['password_confirm'][] = 'Паролите не съвпадат';
            }
        }
        
        if (!empty($errors)) {
            $groups = Group::getActiveGroups();
            $userGroups = $user->getGroups();
            $userGroupIds = array_map(fn($group) => $group->id, $userGroups);
            
            $this->view('admin/users/edit', [
                'title' => 'Редактиране на потребител',
                'showSidebar' => true,
                'user' => $user,
                'groups' => $groups,
                'userGroupIds' => $userGroupIds,
                'errors' => $errors,
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }
        
        try {
            // Update user
            $user->username = $data['username'];
            $user->email = $data['email'];
            $user->first_name = $data['first_name'];
            $user->last_name = $data['last_name'];
            $user->is_active = isset($data['is_active']) ? 1 : 0;
            
            // Update password if provided
            if (!empty($data['password'])) {
                $user->setPassword($data['password']);
            }
            
            if ($user->save()) {
                // Update groups efficiently
                if ($this->app->hasPermission('users.manage_groups')) {
                    // Remove all current groups with single query
                    \Strix\ERP\Core\Database::query(
                        "DELETE FROM user_groups WHERE user_id = ?",
                        [$user->id]
                    );

                    // Add new groups with batch insert
                    if (isset($data['groups']) && is_array($data['groups']) && !empty($data['groups'])) {
                        $currentUser = $this->app->getCurrentUser();
                        $values = [];
                        $placeholders = [];

                        foreach ($data['groups'] as $groupId) {
                            $values[] = $user->id;
                            $values[] = (int) $groupId;
                            $values[] = $currentUser['id'];
                            $values[] = date('Y-m-d H:i:s');
                            $placeholders[] = "(?, ?, ?, ?)";
                        }

                        \Strix\ERP\Core\Database::query(
                            "INSERT INTO user_groups (user_id, group_id, assigned_by, assigned_at) VALUES " .
                            implode(", ", $placeholders),
                            $values
                        );
                    }
                }
                
                $this->redirectWithMessage('/admin/users', 'success', 'Потребителят е обновен успешно');
            } else {
                throw new \Exception('Грешка при запазване на потребителя');
            }
        } catch (\Exception $e) {
            $groups = Group::getActiveGroups();
            $userGroups = $user->getGroups();
            $userGroupIds = array_map(fn($group) => $group->id, $userGroups);
            
            $this->view('admin/users/edit', [
                'title' => 'Редактиране на потребител',
                'showSidebar' => true,
                'user' => $user,
                'groups' => $groups,
                'userGroupIds' => $userGroupIds,
                'error' => 'Грешка при обновяване на потребителя: ' . $e->getMessage(),
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        }
    }
    
    public function delete(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('users.delete');
        
        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }
        
        $user = User::find($id);
        if (!$user) {
            $this->json(['error' => 'Потребителят не е намерен'], 404);
            return;
        }
        
        // Prevent deleting current user
        $currentUser = $this->app->getCurrentUser();
        if ($user->id === $currentUser['id']) {
            $this->json(['error' => 'Не можете да изтриете собствения си акаунт'], 400);
            return;
        }
        
        try {
            if ($user->delete()) {
                $this->json(['success' => true, 'message' => 'Потребителят е изтрит успешно']);
            } else {
                $this->json(['error' => 'Грешка при изтриване на потребителя'], 500);
            }
        } catch (\Exception $e) {
            $this->json(['error' => 'Грешка при изтриване: ' . $e->getMessage()], 500);
        }
    }
}
