<?php

namespace Strix\ERP\Controllers;

use Strix\ERP\Core\Controller;
use Strix\ERP\Services\StrixBudgetClient;
use Strix\ERP\Models\UserStrixBudgetSettings;
use Strix\ERP\Models\StrixBudget\BaseStrixBudgetModel;
use Strix\ERP\Models\StrixBudget\BankAccount;
use Strix\ERP\Models\StrixBudget\Transaction;
use Strix\ERP\Models\StrixBudget\Transfer;
use Strix\ERP\Models\StrixBudget\Counterparty;
use Strix\ERP\Models\StrixBudget\TransactionType;

class StrixBudgetController extends Controller
{
    private ?StrixBudgetClient $client = null;
    private ?UserStrixBudgetSettings $settings = null;
    
    public function __construct()
    {
        parent::__construct();
        $this->initializeClient();
    }
    
    /**
     * Initialize StrixBudget client with user settings
     */
    private function initializeClient(): void
    {
        $this->requireAuth();

        $currentUser = $this->app->getCurrentUser();
        $this->settings = UserStrixBudgetSettings::getByUserId($currentUser['id']);

        if ($this->settings && $this->settings->isConfigured()) {
            $config = $this->settings->getClientConfig();
            $this->client = new StrixBudgetClient($config['api_url']);

            // Authenticate based on method
            $authSuccess = false;
            if ($this->settings->usesCredentials()) {
                // Login with credentials to get token
                try {
                    $loginResult = $this->client->loginAndGetToken($config['api_email'], $config['api_password']);
                    if ($loginResult['success']) {
                        $this->client->setToken($loginResult['token']);
                        $authSuccess = true;
                    } else {
                        error_log("StrixBudget login failed: " . ($loginResult['message'] ?? 'Unknown error'));
                    }
                } catch (\Exception $e) {
                    error_log("StrixBudget login exception: " . $e->getMessage());
                }
            } else {
                // Use existing token
                if (!empty($config['api_token'])) {
                    $this->client->setToken($config['api_token']);
                    $authSuccess = true;
                }
            }

            // Set client for all StrixBudget models only if auth was successful
            if ($authSuccess) {
                BaseStrixBudgetModel::setClient($this->client);
            } else {
                $this->client = null; // Clear client if auth failed
            }
        }
    }
    
    /**
     * StrixBudget dashboard
     */
    public function dashboard(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.view');
        
        $data = [
            'title' => 'StrixBudget Dashboard',
            'showSidebar' => true,
            'isConfigured' => $this->settings && $this->settings->isConfigured(),
            'settings' => $this->settings
        ];
        
        if ($data['isConfigured'] && $this->client) {
            try {
                // Get dashboard statistics
                $stats = $this->getDashboardStats();
                $data = array_merge($data, $stats);
            } catch (\Exception $e) {
                $data['error'] = 'Грешка при зареждане на данни: ' . $e->getMessage();
                error_log("StrixBudget dashboard error: " . $e->getMessage());
            }
        } elseif ($data['isConfigured'] && !$this->client) {
            $data['error'] = 'Грешка при инициализация на StrixBudget клиента. Моля, проверете настройките.';
        }
        
        $this->view('admin/strixbudget/dashboard', $data);
    }
    
    /**
     * Get dashboard statistics
     */
    private function getDashboardStats(): array
    {
        $stats = [
            'total_accounts' => 0,
            'total_balance' => 0,
            'recent_transactions' => [],
            'recent_transfers' => [],
            'account_balances' => [],
            'monthly_summary' => []
        ];

        if (!$this->client) {
            error_log("StrixBudget client not initialized in getDashboardStats");
            return $stats;
        }

        error_log("StrixBudget client initialized, getting dashboard stats...");

        try {
            // Get user statistics
            try {
                $userStats = $this->client->getUserStatistics();
                if ($userStats['success'] && isset($userStats['data'])) {
                    $stats = array_merge($stats, $userStats['data']);
                }
                error_log("Successfully loaded user statistics");
            } catch (\Exception $e) {
                error_log("Error loading user statistics: " . $e->getMessage());
            }

            // Get bank accounts
            try {
                $accounts = BankAccount::getActive();
                $stats['total_accounts'] = count($accounts);
                $stats['account_balances'] = $accounts;

                // Calculate total balance
                $totalBalance = 0;
                foreach ($accounts as $account) {
                    $totalBalance += (float) ($account->balance ?? 0);
                }
                $stats['total_balance'] = $totalBalance;
                error_log("Successfully loaded " . count($accounts) . " bank accounts");
            } catch (\Exception $e) {
                error_log("Error loading bank accounts: " . $e->getMessage());
                $stats['account_balances'] = [];
            }

            // Get recent transactions (last 10)
            try {
                $recentTransactions = Transaction::getFiltered(['per_page' => 10]);
                $stats['recent_transactions'] = array_slice($recentTransactions, 0, 10);
                error_log("Successfully loaded " . count($recentTransactions) . " transactions");
            } catch (\Exception $e) {
                error_log("Error loading recent transactions: " . $e->getMessage());
                error_log("Transaction error trace: " . $e->getTraceAsString());
                $stats['recent_transactions'] = [];
            }

            // Get recent transfers (last 5)
            try {
                $recentTransfers = Transfer::all();
                $stats['recent_transfers'] = array_slice($recentTransfers, 0, 5);
                error_log("Successfully loaded " . count($recentTransfers) . " transfers");
            } catch (\Exception $e) {
                error_log("Error loading transfers: " . $e->getMessage());
                $stats['recent_transfers'] = [];
            }

        } catch (\Exception $e) {
            error_log("Error getting dashboard stats: " . $e->getMessage());
            error_log("Dashboard error trace: " . $e->getTraceAsString());
            // Don't re-throw, return partial stats instead
        }

        return $stats;
    }
    
    /**
     * StrixBudget settings page
     */
    public function settings(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        $currentUser = $this->app->getCurrentUser();
        $settings = UserStrixBudgetSettings::getOrCreateForUser($currentUser['id']);
        
        $this->view('admin/strixbudget/settings', [
            'title' => 'StrixBudget Настройки',
            'showSidebar' => true,
            'settings' => $settings,
            'errors' => [],
            'old_input' => [],
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }
    
    /**
     * Update StrixBudget settings
     */
    public function updateSettings(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Невалидна заявка');
        }
        
        $data = $this->getAllInput();
        $currentUser = $this->app->getCurrentUser();

        // Validate input based on auth method
        $authMethod = $data['auth_method'] ?? 'token';

        $validationRules = [
            'api_url' => 'required|url',
            'auth_method' => 'required|in:token,credentials'
        ];

        if ($authMethod === 'credentials') {
            $validationRules['api_email'] = 'required|email';
            $validationRules['api_password'] = 'required';
        } else {
            $validationRules['api_token'] = 'required';
        }

        $errors = $this->validate($validationRules);
        
        if (!empty($errors)) {
            $settings = UserStrixBudgetSettings::getOrCreateForUser($currentUser['id']);
            
            $this->view('admin/strixbudget/settings', [
                'title' => 'StrixBudget Настройки',
                'showSidebar' => true,
                'settings' => $settings,
                'errors' => $errors,
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }
        
        try {
            $settings = UserStrixBudgetSettings::getOrCreateForUser($currentUser['id']);
            $settings->api_url = rtrim($data['api_url'], '/');
            $settings->auth_method = $authMethod;
            $settings->is_active = isset($data['is_active']) ? 1 : 0;

            if ($authMethod === 'credentials') {
                $settings->setEncryptedEmail($data['api_email']);
                $settings->setEncryptedPassword($data['api_password']);

                // Test login and get token
                $testClient = new StrixBudgetClient($settings->api_url);
                $loginResult = $testClient->loginAndGetToken($data['api_email'], $data['api_password']);

                if (!$loginResult['success']) {
                    throw new \Exception('Грешка при влизане: ' . $loginResult['message']);
                }

                // Store the obtained token as well
                $settings->setEncryptedToken($loginResult['token']);
            } else {
                $settings->setEncryptedToken($data['api_token']);

                // Clear credentials if switching from credentials to token
                $settings->api_email = null;
                $settings->api_password = null;
            }

            if ($settings->save()) {
                $message = $authMethod === 'credentials'
                    ? 'Настройките са запазени успешно. Получен е API токен от сървъра.'
                    : 'Настройките са запазени успешно';
                $this->redirectWithMessage('/admin/strixbudget/settings', 'success', $message);
            } else {
                throw new \Exception('Грешка при запазване на настройките');
            }
        } catch (\Exception $e) {
            $settings = UserStrixBudgetSettings::getOrCreateForUser($currentUser['id']);
            
            $this->view('admin/strixbudget/settings', [
                'title' => 'StrixBudget Настройки',
                'showSidebar' => true,
                'settings' => $settings,
                'error' => 'Грешка при запазване: ' . $e->getMessage(),
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        }
    }
    
    /**
     * Test StrixBudget API connection
     */
    public function testConnection(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');

        if (!$this->validateCsrfToken()) {
            $this->json(['success' => false, 'message' => 'Невалидна заявка'], 400);
            return;
        }

        $currentUser = $this->app->getCurrentUser();
        $settings = UserStrixBudgetSettings::getByUserId($currentUser['id']);

        if (!$settings || !$settings->isConfigured()) {
            $this->json(['success' => false, 'message' => 'StrixBudget настройките не са конфигурирани'], 400);
            return;
        }
        
        try {
            $config = $settings->getClientConfig();
            $testClient = new StrixBudgetClient($config['api_url']);

            // Authenticate based on method
            if ($settings->usesCredentials()) {
                $loginResult = $testClient->loginAndGetToken($config['api_email'], $config['api_password']);
                if (!$loginResult['success']) {
                    $this->json([
                        'success' => false,
                        'message' => 'Грешка при влизане: ' . $loginResult['message']
                    ]);
                    return;
                }
                $testClient->setToken($loginResult['token']);
            } else {
                $testClient->setToken($config['api_token']);
            }

            $result = $testClient->testConnection();

            if ($result['success']) {
                $this->json([
                    'success' => true,
                    'message' => $result['message'],
                    'user' => $result['user'] ?? null,
                    'auth_method' => $settings->getAuthMethod(),
                    'mock_mode' => $result['mock_mode'] ?? false,
                    'api_url' => $result['api_url'] ?? $config['api_url']
                ]);
            } else {
                $this->json([
                    'success' => false,
                    'message' => $result['message'],
                    'error_details' => $result['error_details'] ?? null,
                    'debug_info' => $result['debug_info'] ?? null
                ]);
            }
        } catch (\Exception $e) {
            error_log("StrixBudget test connection error: " . $e->getMessage());
            $this->json([
                'success' => false,
                'message' => 'Грешка при тестване на връзката: ' . $e->getMessage(),
                'debug_info' => [
                    'exception' => get_class($e),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]
            ], 500);
        }
    }

    /**
     * Show API debug information
     */
    public function debugInfo(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');

        $currentUser = $this->app->getCurrentUser();
        $settings = UserStrixBudgetSettings::getByUserId($currentUser['id']);

        $debugInfo = [
            'settings_configured' => $settings && $settings->isConfigured(),
            'environment' => [
                'STRIXBUDGET_API_URL' => $_ENV['STRIXBUDGET_API_URL'] ?? 'not set',
                'STRIXBUDGET_MOCK_MODE' => $_ENV['STRIXBUDGET_MOCK_MODE'] ?? 'not set',
                'STRIXBUDGET_TIMEOUT' => $_ENV['STRIXBUDGET_TIMEOUT'] ?? 'not set',
                'STRIXBUDGET_VERIFY_SSL' => $_ENV['STRIXBUDGET_VERIFY_SSL'] ?? 'not set'
            ]
        ];

        if ($settings && $settings->isConfigured()) {
            $config = $settings->getClientConfig();
            $client = new StrixBudgetClient($config['api_url']);
            $debugInfo['client'] = $client->getDebugInfo();
            $debugInfo['user_settings'] = [
                'api_url' => $settings->api_url,
                'auth_method' => $settings->getAuthMethod(),
                'is_active' => $settings->isActive(),
                'has_token' => !empty($settings->api_token),
                'has_email' => !empty($settings->api_email),
                'has_password' => !empty($settings->api_password)
            ];
        }

        $this->json([
            'success' => true,
            'data' => $debugInfo
        ]);
    }

    /**
     * Delete StrixBudget settings
     */
    public function deleteSettings(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');

        if (!$this->validateCsrfToken()) {
            $this->json(['success' => false, 'message' => 'Невалидна заявка'], 400);
            return;
        }
        
        $currentUser = $this->app->getCurrentUser();
        
        try {
            if (UserStrixBudgetSettings::deleteForUser($currentUser['id'])) {
                $this->json(['success' => true, 'message' => 'Настройките са изтрити успешно']);
            } else {
                $this->json(['success' => false, 'message' => 'Грешка при изтриване на настройките']);
            }
        } catch (\Exception $e) {
            $this->json(['success' => false, 'message' => 'Грешка: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Get StrixBudget settings as JSON
     */
    public function getSettings(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.view');
        
        $currentUser = $this->app->getCurrentUser();
        $settings = UserStrixBudgetSettings::getByUserId($currentUser['id']);
        
        if ($settings) {
            $this->json([
                'success' => true,
                'data' => [
                    'api_url' => $settings->api_url ?? '',
                    'auth_method' => $settings->getAuthMethod(),
                    'has_token' => !empty($settings->api_token),
                    'masked_token' => $settings->getMaskedToken(),
                    'has_email' => !empty($settings->api_email),
                    'masked_email' => $settings->getMaskedEmail(),
                    'has_password' => !empty($settings->api_password),
                    'masked_password' => $settings->getMaskedPassword(),
                    'is_active' => $settings->isActive(),
                    'is_configured' => $settings->isConfigured()
                ]
            ]);
        } else {
            $this->json([
                'success' => true,
                'data' => [
                    'api_url' => '',
                    'auth_method' => 'token',
                    'has_token' => false,
                    'masked_token' => 'Не е настроен',
                    'has_email' => false,
                    'masked_email' => 'Не е настроен',
                    'has_password' => false,
                    'masked_password' => 'Не е настроена',
                    'is_active' => false,
                    'is_configured' => false
                ]
            ]);
        }
    }
}
