<?php

namespace Strix\ERP\Controllers\StrixBudget;

use Strix\ERP\Models\StrixBudget\Counterparty;

class CounterpartyController extends BaseStrixBudgetController
{
    /**
     * List all counterparties
     */
    public function index(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.view');
        $this->requireStrixBudgetClient();

        $search = $this->getInput('search', '');
        $perPage = min(100, max(1, (int) $this->getInput('per_page', 20)));

        try {
            if (!empty($search)) {
                $counterparties = Counterparty::search($search, $perPage);
            } else {
                $counterparties = Counterparty::paginate(1, $perPage);
            }

            $this->renderStrixBudgetView('admin/strixbudget/counterparties/index', [
                'title' => 'Контрагенти',
                'counterparties' => $counterparties,
                'search' => $search,
                'per_page' => $perPage
            ]);
        } catch (\Exception $e) {
            $errorInfo = $this->handleStrixBudgetException($e, 'Грешка при зареждане на контрагенти');

            $this->renderStrixBudgetView('admin/strixbudget/counterparties/index', [
                'title' => 'Контрагенти',
                'counterparties' => [],
                'search' => $search,
                'per_page' => $perPage,
                'error' => $errorInfo['error']
            ]);
        }
    }
    
    /**
     * Show create form
     */
    public function create(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        $this->requireStrixBudgetClient();

        $this->renderStrixBudgetView('admin/strixbudget/counterparties/create', [
            'title' => 'Създаване на контрагент',
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }
    
    /**
     * Store new counterparty
     */
    public function store(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/strixbudget/counterparties/create', 'error', 'Невалидна заявка');
        }
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        $data = $this->getAllInput();
        
        // Validate input
        $errors = $this->validate([
            'name' => 'required|max:255'
        ]);
        
        if (!empty($errors)) {
            $this->view('admin/strixbudget/counterparties/create', [
                'title' => 'Създаване на контрагент',
                'showSidebar' => true,
                'errors' => $errors,
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }
        
        try {
            $counterpartyData = [
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'email' => $data['email'] ?? '',
                'phone' => $data['phone'] ?? ''
            ];
            
            $counterparty = Counterparty::create($counterpartyData);
            
            if ($counterparty) {
                $this->redirectWithMessage('/admin/strixbudget/counterparties', 'success', 'Контрагентът е създаден успешно');
            } else {
                throw new \Exception('Грешка при създаване на контрагента');
            }
        } catch (\Exception $e) {
            $this->view('admin/strixbudget/counterparties/create', [
                'title' => 'Създаване на контрагент',
                'showSidebar' => true,
                'error' => 'Грешка при създаване: ' . $e->getMessage(),
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        }
    }
    
    /**
     * Show specific counterparty
     */
    public function show(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.view');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $counterparty = Counterparty::find($id);
            
            if (!$counterparty) {
                $this->redirectWithMessage('/admin/strixbudget/counterparties', 'error', 'Контрагентът не е намерен');
            }
            
            $statistics = $counterparty->getStatistics();
            $transactions = $counterparty->getTransactions();
            
            $this->view('admin/strixbudget/counterparties/show', [
                'title' => 'Преглед на контрагент: ' . $counterparty->getDisplayName(),
                'showSidebar' => true,
                'counterparty' => $counterparty,
                'statistics' => $statistics,
                'transactions' => $transactions
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/counterparties', 'error', 'Грешка при зареждане на контрагента: ' . $e->getMessage());
        }
    }
    
    /**
     * Show edit form
     */
    public function edit(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $counterparty = Counterparty::find($id);
            
            if (!$counterparty) {
                $this->redirectWithMessage('/admin/strixbudget/counterparties', 'error', 'Контрагентът не е намерен');
            }
            
            $this->view('admin/strixbudget/counterparties/edit', [
                'title' => 'Редактиране на контрагент',
                'showSidebar' => true,
                'counterparty' => $counterparty,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/counterparties', 'error', 'Грешка при зареждане на контрагента: ' . $e->getMessage());
        }
    }
    
    /**
     * Update counterparty
     */
    public function update(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/strixbudget/counterparties', 'error', 'Невалидна заявка');
        }
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $counterparty = Counterparty::find($id);
            
            if (!$counterparty) {
                $this->redirectWithMessage('/admin/strixbudget/counterparties', 'error', 'Контрагентът не е намерен');
            }
            
            $data = $this->getAllInput();
            
            // Validate input
            $errors = $this->validate([
                'name' => 'required|max:255'
            ]);
            
            if (!empty($errors)) {
                $this->view('admin/strixbudget/counterparties/edit', [
                    'title' => 'Редактиране на контрагент',
                    'showSidebar' => true,
                    'counterparty' => $counterparty,
                    'errors' => $errors,
                    'old_input' => $data,
                    'csrf_token' => $this->generateCsrfToken()
                ]);
                return;
            }
            
            $counterparty->name = $data['name'];
            $counterparty->description = $data['description'] ?? '';
            $counterparty->email = $data['email'] ?? '';
            $counterparty->phone = $data['phone'] ?? '';
            
            if ($counterparty->save()) {
                $this->redirectWithMessage('/admin/strixbudget/counterparties/' . $id, 'success', 'Контрагентът е обновен успешно');
            } else {
                throw new \Exception('Грешка при обновяване на контрагента');
            }
        } catch (\Exception $e) {
            $counterparty = Counterparty::find($id);
            $data = $this->getAllInput();
            
            $this->view('admin/strixbudget/counterparties/edit', [
                'title' => 'Редактиране на контрагент',
                'showSidebar' => true,
                'counterparty' => $counterparty,
                'error' => 'Грешка при обновяване: ' . $e->getMessage(),
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        }
    }
    
    /**
     * Delete counterparty
     */
    public function delete(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }
        
        if (!$this->client) {
            $this->json(['error' => 'StrixBudget не е конфигуриран'], 400);
            return;
        }
        
        try {
            $counterparty = Counterparty::find($id);
            
            if (!$counterparty) {
                $this->json(['error' => 'Контрагентът не е намерен'], 404);
                return;
            }
            
            if ($counterparty->delete()) {
                $this->json(['success' => true, 'message' => 'Контрагентът е изтрит успешно']);
            } else {
                $this->json(['error' => 'Грешка при изтриване на контрагента'], 500);
            }
        } catch (\Exception $e) {
            $this->json(['error' => 'Грешка: ' . $e->getMessage()], 500);
        }
    }
}
