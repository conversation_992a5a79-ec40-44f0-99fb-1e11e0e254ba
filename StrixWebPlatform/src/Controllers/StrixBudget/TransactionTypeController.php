<?php

namespace Strix\ERP\Controllers\StrixBudget;

use Strix\ERP\Models\StrixBudget\TransactionType;

class TransactionTypeController extends BaseStrixBudgetController
{
    
    /**
     * List all transaction types
     */
    public function index(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.view');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $transactionTypes = TransactionType::all();
            
            $this->view('admin/strixbudget/transaction-types/index', [
                'title' => 'Типове транзакции',
                'showSidebar' => true,
                'transactionTypes' => $transactionTypes
            ]);
        } catch (\Exception $e) {
            $this->view('admin/strixbudget/transaction-types/index', [
                'title' => 'Типове транзакции',
                'showSidebar' => true,
                'transactionTypes' => [],
                'error' => 'Грешка при зареждане на типове транзакции: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Show create form
     */
    public function create(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        $this->view('admin/strixbudget/transaction-types/create', [
            'title' => 'Създаване на тип транзакция',
            'showSidebar' => true,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }
    
    /**
     * Store new transaction type
     */
    public function store(): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/strixbudget/transaction-types/create', 'error', 'Невалидна заявка');
        }
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        $data = $this->getAllInput();
        
        // Validate input
        $errors = $this->validate([
            'name' => 'required|max:255'
        ]);
        
        if (!empty($errors)) {
            $this->view('admin/strixbudget/transaction-types/create', [
                'title' => 'Създаване на тип транзакция',
                'showSidebar' => true,
                'errors' => $errors,
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }
        
        try {
            $typeData = [
                'name' => $data['name'],
                'description' => $data['description'] ?? ''
            ];
            
            $transactionType = TransactionType::create($typeData);
            
            if ($transactionType) {
                $this->redirectWithMessage('/admin/strixbudget/transaction-types', 'success', 'Типът транзакция е създаден успешно');
            } else {
                throw new \Exception('Грешка при създаване на типа транзакция');
            }
        } catch (\Exception $e) {
            $this->view('admin/strixbudget/transaction-types/create', [
                'title' => 'Създаване на тип транзакция',
                'showSidebar' => true,
                'error' => 'Грешка при създаване: ' . $e->getMessage(),
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        }
    }
    
    /**
     * Show specific transaction type
     */
    public function show(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.view');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $transactionType = TransactionType::find($id);
            
            if (!$transactionType) {
                $this->redirectWithMessage('/admin/strixbudget/transaction-types', 'error', 'Типът транзакция не е намерен');
            }
            
            $statistics = $transactionType->getStatistics();
            $transactions = $transactionType->getTransactions();
            
            $this->view('admin/strixbudget/transaction-types/show', [
                'title' => 'Преглед на тип транзакция: ' . $transactionType->getDisplayName(),
                'showSidebar' => true,
                'transactionType' => $transactionType,
                'statistics' => $statistics,
                'transactions' => $transactions
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/transaction-types', 'error', 'Грешка при зареждане на типа транзакция: ' . $e->getMessage());
        }
    }
    
    /**
     * Show edit form
     */
    public function edit(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $transactionType = TransactionType::find($id);
            
            if (!$transactionType) {
                $this->redirectWithMessage('/admin/strixbudget/transaction-types', 'error', 'Типът транзакция не е намерен');
            }
            
            $this->view('admin/strixbudget/transaction-types/edit', [
                'title' => 'Редактиране на тип транзакция',
                'showSidebar' => true,
                'transactionType' => $transactionType,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        } catch (\Exception $e) {
            $this->redirectWithMessage('/admin/strixbudget/transaction-types', 'error', 'Грешка при зареждане на типа транзакция: ' . $e->getMessage());
        }
    }
    
    /**
     * Update transaction type
     */
    public function update(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/strixbudget/transaction-types', 'error', 'Невалидна заявка');
        }
        
        if (!$this->client) {
            $this->redirectWithMessage('/admin/strixbudget/settings', 'error', 'Моля, конфигурирайте StrixBudget настройките първо');
        }
        
        try {
            $transactionType = TransactionType::find($id);
            
            if (!$transactionType) {
                $this->redirectWithMessage('/admin/strixbudget/transaction-types', 'error', 'Типът транзакция не е намерен');
            }
            
            $data = $this->getAllInput();
            
            // Validate input
            $errors = $this->validate([
                'name' => 'required|max:255'
            ]);
            
            if (!empty($errors)) {
                $this->view('admin/strixbudget/transaction-types/edit', [
                    'title' => 'Редактиране на тип транзакция',
                    'showSidebar' => true,
                    'transactionType' => $transactionType,
                    'errors' => $errors,
                    'old_input' => $data,
                    'csrf_token' => $this->generateCsrfToken()
                ]);
                return;
            }
            
            $transactionType->name = $data['name'];
            $transactionType->description = $data['description'] ?? '';
            
            if ($transactionType->save()) {
                $this->redirectWithMessage('/admin/strixbudget/transaction-types/' . $id, 'success', 'Типът транзакция е обновен успешно');
            } else {
                throw new \Exception('Грешка при обновяване на типа транзакция');
            }
        } catch (\Exception $e) {
            $transactionType = TransactionType::find($id);
            $data = $this->getAllInput();
            
            $this->view('admin/strixbudget/transaction-types/edit', [
                'title' => 'Редактиране на тип транзакция',
                'showSidebar' => true,
                'transactionType' => $transactionType,
                'error' => 'Грешка при обновяване: ' . $e->getMessage(),
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        }
    }
    
    /**
     * Delete transaction type
     */
    public function delete(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('strixbudget.manage');
        
        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }
        
        if (!$this->client) {
            $this->json(['error' => 'StrixBudget не е конфигуриран'], 400);
            return;
        }
        
        try {
            $transactionType = TransactionType::find($id);
            
            if (!$transactionType) {
                $this->json(['error' => 'Типът транзакция не е намерен'], 404);
                return;
            }
            
            if ($transactionType->delete()) {
                $this->json(['success' => true, 'message' => 'Типът транзакция е изтрит успешно']);
            } else {
                $this->json(['error' => 'Грешка при изтриване на типа транзакция'], 500);
            }
        } catch (\Exception $e) {
            $this->json(['error' => 'Грешка: ' . $e->getMessage()], 500);
        }
    }
}
