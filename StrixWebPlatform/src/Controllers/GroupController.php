<?php

namespace Strix\ERP\Controllers;

use Strix\ERP\Core\Controller;
use Strix\ERP\Models\Group;
use Strix\ERP\Models\Permission;

class GroupController extends Controller
{
    public function index(): void
    {
        $this->requireAuth();
        $this->requirePermission('groups.view');

        $search = $this->getInput('search', '');
        $page = max(1, (int) $this->getInput('page', 1));
        $perPage = 20;

        // Build optimized query with proper pagination
        $whereClause = '';
        $params = [];

        if ($search) {
            $whereClause = "WHERE (name LIKE ? OR description LIKE ?)";
            $searchTerm = "%$search%";
            $params = [$searchTerm, $searchTerm];
        }

        // Get total count efficiently
        $totalGroups = (int) \Strix\ERP\Core\Database::fetchColumn(
            "SELECT COUNT(*) FROM groups $whereClause",
            $params
        );

        // Get paginated data with user count
        $offset = ($page - 1) * $perPage;
        $results = \Strix\ERP\Core\Database::fetchAll(
            "SELECT g.*, COUNT(ug.user_id) as user_count
             FROM groups g
             LEFT JOIN user_groups ug ON g.id = ug.group_id
             $whereClause
             GROUP BY g.id
             ORDER BY g.created_at DESC
             LIMIT $perPage OFFSET $offset",
            $params
        );

        // Convert to Group objects
        $groups = [];
        foreach ($results as $groupData) {
            $group = new Group();
            foreach ($groupData as $key => $value) {
                $group->$key = $value;
            }
            $groups[] = $group;
        }

        $totalPages = ceil($totalGroups / $perPage);

        $this->view('admin/groups/index', [
            'title' => 'Управление на групи',
            'showSidebar' => true,
            'groups' => $groups,
            'search' => $search,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'totalGroups' => $totalGroups
        ]);
    }
    
    public function create(): void
    {
        $this->requireAuth();
        $this->requirePermission('groups.create');
        
        $permissions = Permission::getGroupedByModule();
        
        $this->view('admin/groups/create', [
            'title' => 'Създаване на група',
            'showSidebar' => true,
            'permissions' => $permissions,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }
    
    public function store(): void
    {
        $this->requireAuth();
        $this->requirePermission('groups.create');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/groups/create', 'error', 'Невалидна заявка');
        }
        
        $data = $this->getAllInput();
        
        // Validate input
        $errors = $this->validate([
            'name' => 'required|min:3|max:50|unique:groups,name',
            'description' => 'max:255'
        ]);
        
        if (!empty($errors)) {
            $permissions = Permission::getGroupedByModule();
            $this->view('admin/groups/create', [
                'title' => 'Създаване на група',
                'showSidebar' => true,
                'permissions' => $permissions,
                'errors' => $errors,
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }
        
        try {
            // Create group
            $group = new Group();
            $group->name = $data['name'];
            $group->description = $data['description'] ?? '';
            $group->is_active = isset($data['is_active']) ? 1 : 0;
            
            if ($group->save()) {
                // Assign permissions
                if (isset($data['permissions']) && is_array($data['permissions'])) {
                    $currentUser = $this->app->getCurrentUser();
                    foreach ($data['permissions'] as $permissionId) {
                        $group->addPermission((int) $permissionId, $currentUser['id']);
                    }
                }
                
                $this->redirectWithMessage('/admin/groups', 'success', 'Групата е създадена успешно');
            } else {
                throw new \Exception('Грешка при запазване на групата');
            }
        } catch (\Exception $e) {
            $permissions = Permission::getGroupedByModule();
            $this->view('admin/groups/create', [
                'title' => 'Създаване на група',
                'showSidebar' => true,
                'permissions' => $permissions,
                'error' => 'Грешка при създаване на групата: ' . $e->getMessage(),
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        }
    }
    
    public function edit(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('groups.edit');
        
        $group = Group::find($id);
        if (!$group) {
            $this->redirectWithMessage('/admin/groups', 'error', 'Групата не е намерена');
        }
        
        $permissions = Permission::getGroupedByModule();
        $groupPermissions = $group->getPermissions();
        $groupPermissionIds = array_map(fn($permission) => $permission->id, $groupPermissions);
        
        $this->view('admin/groups/edit', [
            'title' => 'Редактиране на група',
            'showSidebar' => true,
            'group' => $group,
            'permissions' => $permissions,
            'groupPermissionIds' => $groupPermissionIds,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }
    
    public function update(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('groups.edit');
        
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/admin/groups', 'error', 'Невалидна заявка');
        }
        
        $group = Group::find($id);
        if (!$group) {
            $this->redirectWithMessage('/admin/groups', 'error', 'Групата не е намерена');
        }
        
        $data = $this->getAllInput();
        
        // Validate input
        $errors = $this->validate([
            'name' => 'required|min:3|max:50',
            'description' => 'max:255'
        ]);
        
        // Check for unique name (excluding current group)
        $existingGroup = Group::findByName($data['name']);
        if ($existingGroup && $existingGroup->id !== $group->id) {
            $errors['name'][] = 'Името на групата вече съществува';
        }
        
        if (!empty($errors)) {
            $permissions = Permission::getGroupedByModule();
            $groupPermissions = $group->getPermissions();
            $groupPermissionIds = array_map(fn($permission) => $permission->id, $groupPermissions);
            
            $this->view('admin/groups/edit', [
                'title' => 'Редактиране на група',
                'showSidebar' => true,
                'group' => $group,
                'permissions' => $permissions,
                'groupPermissionIds' => $groupPermissionIds,
                'errors' => $errors,
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }
        
        try {
            // Update group
            $group->name = $data['name'];
            $group->description = $data['description'] ?? '';
            $group->is_active = isset($data['is_active']) ? 1 : 0;
            
            if ($group->save()) {
                // Update permissions
                if ($this->app->hasPermission('groups.manage_permissions')) {
                    $currentUser = $this->app->getCurrentUser();
                    $newPermissionIds = isset($data['permissions']) && is_array($data['permissions']) 
                        ? array_map('intval', $data['permissions']) 
                        : [];
                    
                    $group->syncPermissions($newPermissionIds, $currentUser['id']);
                }
                
                $this->redirectWithMessage('/admin/groups', 'success', 'Групата е обновена успешно');
            } else {
                throw new \Exception('Грешка при запазване на групата');
            }
        } catch (\Exception $e) {
            $permissions = Permission::getGroupedByModule();
            $groupPermissions = $group->getPermissions();
            $groupPermissionIds = array_map(fn($permission) => $permission->id, $groupPermissions);
            
            $this->view('admin/groups/edit', [
                'title' => 'Редактиране на група',
                'showSidebar' => true,
                'group' => $group,
                'permissions' => $permissions,
                'groupPermissionIds' => $groupPermissionIds,
                'error' => 'Грешка при обновяване на групата: ' . $e->getMessage(),
                'old_input' => $data,
                'csrf_token' => $this->generateCsrfToken()
            ]);
        }
    }
    
    public function delete(int $id): void
    {
        $this->requireAuth();
        $this->requirePermission('groups.delete');
        
        if (!$this->validateCsrfToken()) {
            $this->json(['error' => 'Невалидна заявка'], 400);
            return;
        }
        
        $group = Group::find($id);
        if (!$group) {
            $this->json(['error' => 'Групата не е намерена'], 404);
            return;
        }
        
        // Check if group has users
        $userCount = $group->getUserCount();
        if ($userCount > 0) {
            $this->json(['error' => "Не можете да изтриете група с {$userCount} потребители"], 400);
            return;
        }
        
        // Prevent deleting administrators group
        if ($group->name === 'administrators') {
            $this->json(['error' => 'Не можете да изтриете групата на администраторите'], 400);
            return;
        }
        
        try {
            if ($group->delete()) {
                $this->json(['success' => true, 'message' => 'Групата е изтрита успешно']);
            } else {
                $this->json(['error' => 'Грешка при изтриване на групата'], 500);
            }
        } catch (\Exception $e) {
            $this->json(['error' => 'Грешка при изтриване: ' . $e->getMessage()], 500);
        }
    }
}
