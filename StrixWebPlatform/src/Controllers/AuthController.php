<?php

namespace Strix\ERP\Controllers;

use Strix\ERP\Core\Controller;
use Strix\ERP\Models\User;

class AuthController extends Controller
{
    public function showLogin(): void
    {
        // If already logged in, redirect to admin
        if ($this->app->isLoggedIn()) {
            $this->redirect('/admin');
        }

        $this->view('auth/login', [
            'title' => 'Вход в системата',
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    public function login(): void
    {
        // Validate CSRF token
        if (!$this->validateCsrfToken()) {
            $this->redirectWithMessage('/login', 'error', 'Невалидна заявка');
        }

        $username = $this->getInput('username');
        $password = $this->getInput('password');
        $remember = $this->getInput('remember', false);

        // Validate input
        $errors = $this->validate([
            'username' => 'required',
            'password' => 'required'
        ]);

        if (!empty($errors)) {
            $this->view('auth/login', [
                'title' => 'Вход в системата',
                'errors' => $errors,
                'old_input' => ['username' => $username],
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }

        // Find user by username or email
        $user = User::findByUsername($username) ?? User::findByEmail($username);

        if (!$user) {
            $this->view('auth/login', [
                'title' => 'Вход в системата',
                'error' => 'Невалидно потребителско име или парола',
                'old_input' => ['username' => $username],
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }

        // Check if account is locked
        if ($user->isLocked()) {
            $this->view('auth/login', [
                'title' => 'Вход в системата',
                'error' => 'Акаунтът е временно заключен поради множество неуспешни опити за вход',
                'old_input' => ['username' => $username],
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }

        // Check if account is active
        if (!$user->isActive()) {
            $this->view('auth/login', [
                'title' => 'Вход в системата',
                'error' => 'Акаунтът е деактивиран',
                'old_input' => ['username' => $username],
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }

        // Verify password
        if (!$user->verifyPassword($password)) {
            $user->incrementFailedAttempts();
            
            // Lock account after max attempts
            $maxAttempts = $this->app->getConfig('security')['max_login_attempts'];
            if ($user->failed_login_attempts >= $maxAttempts) {
                $lockoutDuration = $this->app->getConfig('security')['lockout_duration'] / 60;
                $user->lockAccount($lockoutDuration);
                $errorMessage = "Акаунтът е заключен за {$lockoutDuration} минути поради множество неуспешни опити";
            } else {
                $remaining = $maxAttempts - $user->failed_login_attempts;
                $errorMessage = "Невалидна парола. Остават {$remaining} опита";
            }

            $this->view('auth/login', [
                'title' => 'Вход в системата',
                'error' => $errorMessage,
                'old_input' => ['username' => $username],
                'csrf_token' => $this->generateCsrfToken()
            ]);
            return;
        }

        // Successful login
        $user->resetFailedAttempts();
        $user->updateLastLogin();

        // Get user permissions
        $permissions = $user->getPermissionNames();

        // Set session data
        $this->app->setCurrentUser([
            'id' => $user->id,
            'username' => $user->username,
            'email' => $user->email,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'full_name' => $user->getFullName(),
            'permissions' => $permissions,
            'is_admin' => $user->hasGroup('administrators')
        ]);

        // Set remember me cookie if requested
        if ($remember) {
            $this->setRememberMeCookie($user);
        }

        $this->app->setFlashMessage('success', 'Добре дошли, ' . $user->getFullName() . '!');
        
        // Redirect to intended page or admin dashboard
        $redirectTo = $_SESSION['intended_url'] ?? '/admin';
        unset($_SESSION['intended_url']);
        $this->redirect($redirectTo);
    }

    public function logout(): void
    {
        // Clear remember me cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }

        $this->app->logout();
        $this->app->setFlashMessage('success', 'Успешно излязохте от системата');
        $this->redirect('/login');
    }

    private function setRememberMeCookie(User $user): void
    {
        $token = bin2hex(random_bytes(32));
        $hashedToken = hash('sha256', $token);
        
        // Store hashed token in database (you might want to create a remember_tokens table)
        // For now, we'll use a simple approach with sessions
        $_SESSION['remember_token'] = $hashedToken;
        
        // Set cookie for 30 days
        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
    }

    public function checkRememberMe(): bool
    {
        if (!isset($_COOKIE['remember_token']) || !isset($_SESSION['remember_token'])) {
            return false;
        }

        $token = $_COOKIE['remember_token'];
        $hashedToken = hash('sha256', $token);

        if (hash_equals($_SESSION['remember_token'], $hashedToken)) {
            // Token is valid, user is remembered
            return true;
        }

        // Invalid token, clear cookie
        setcookie('remember_token', '', time() - 3600, '/');
        unset($_SESSION['remember_token']);
        return false;
    }
}
