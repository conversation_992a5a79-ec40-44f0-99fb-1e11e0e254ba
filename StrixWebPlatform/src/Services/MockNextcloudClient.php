<?php

namespace Strix\ERP\Services;

use Exception;

/**
 * Mock Nextcloud Client for testing and demonstration purposes
 * This simulates a Nextcloud server without requiring actual connection
 */
class MockNextcloudClient
{
    private array $mockFiles;
    
    public function __construct()
    {
        $this->initializeMockFiles();
    }
    
    private function initializeMockFiles(): void
    {
        $this->mockFiles = [
            '/' => [
                [
                    'path' => '/Documents',
                    'name' => 'Documents',
                    'size' => 0,
                    'content_type' => '',
                    'last_modified' => date('c', strtotime('-2 days')),
                    'permissions' => 'RGDNVW',
                    'is_directory' => true
                ],
                [
                    'path' => '/Photos',
                    'name' => 'Photos',
                    'size' => 0,
                    'content_type' => '',
                    'last_modified' => date('c', strtotime('-1 week')),
                    'permissions' => 'RGDNVW',
                    'is_directory' => true
                ],
                [
                    'path' => '/ERP_Uploads',
                    'name' => 'ERP_Uploads',
                    'size' => 0,
                    'content_type' => '',
                    'last_modified' => date('c', strtotime('-1 hour')),
                    'permissions' => 'RGDNVW',
                    'is_directory' => true
                ],
                [
                    'path' => '/README.md',
                    'name' => 'README.md',
                    'size' => 1024,
                    'content_type' => 'text/markdown',
                    'last_modified' => date('c', strtotime('-3 days')),
                    'permissions' => 'RGDNVW',
                    'is_directory' => false
                ]
            ],
            '/Documents' => [
                [
                    'path' => '/Documents/Report_2024.pdf',
                    'name' => 'Report_2024.pdf',
                    'size' => 2048576,
                    'content_type' => 'application/pdf',
                    'last_modified' => date('c', strtotime('-1 day')),
                    'permissions' => 'RGDNVW',
                    'is_directory' => false
                ],
                [
                    'path' => '/Documents/Presentation.pptx',
                    'name' => 'Presentation.pptx',
                    'size' => 5242880,
                    'content_type' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                    'last_modified' => date('c', strtotime('-2 hours')),
                    'permissions' => 'RGDNVW',
                    'is_directory' => false
                ],
                [
                    'path' => '/Documents/Spreadsheet.xlsx',
                    'name' => 'Spreadsheet.xlsx',
                    'size' => 1048576,
                    'content_type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'last_modified' => date('c', strtotime('-5 hours')),
                    'permissions' => 'RGDNVW',
                    'is_directory' => false
                ]
            ],
            '/Photos' => [
                [
                    'path' => '/Photos/vacation.jpg',
                    'name' => 'vacation.jpg',
                    'size' => 3145728,
                    'content_type' => 'image/jpeg',
                    'last_modified' => date('c', strtotime('-1 week')),
                    'permissions' => 'RGDNVW',
                    'is_directory' => false
                ],
                [
                    'path' => '/Photos/screenshot.png',
                    'name' => 'screenshot.png',
                    'size' => 524288,
                    'content_type' => 'image/png',
                    'last_modified' => date('c', strtotime('-3 days')),
                    'permissions' => 'RGDNVW',
                    'is_directory' => false
                ],
                [
                    'path' => '/Photos/4K_B.jpg',
                    'name' => '4K_B.jpg',
                    'size' => 8388608,
                    'content_type' => 'image/jpeg',
                    'last_modified' => date('c', strtotime('-2 days')),
                    'permissions' => 'RGDNVW',
                    'is_directory' => false
                ]
            ],
            '/ERP_Uploads' => []
        ];
    }
    
    /**
     * List files and folders in a directory
     */
    public function listDirectory(string $path = '/', string $username = null): array
    {
        $normalizedPath = rtrim($path, '/');
        if ($normalizedPath === '') {
            $normalizedPath = '/';
        }
        
        if (!isset($this->mockFiles[$normalizedPath])) {
            throw new Exception("Directory not found: $path");
        }
        
        return $this->mockFiles[$normalizedPath];
    }
    
    /**
     * Upload a file to Nextcloud
     */
    public function uploadFile(string $localPath, string $remotePath, string $username = null): bool
    {
        if (!file_exists($localPath)) {
            throw new Exception("Local file not found: $localPath");
        }
        
        $fileSize = filesize($localPath);
        $fileName = basename($remotePath);
        $dirPath = dirname($remotePath);
        
        if ($dirPath === '.') {
            $dirPath = '/';
        }
        
        // Ensure directory exists in mock
        if (!isset($this->mockFiles[$dirPath])) {
            $this->mockFiles[$dirPath] = [];
        }
        
        // Add file to mock directory
        $this->mockFiles[$dirPath][] = [
            'path' => $remotePath,
            'name' => $fileName,
            'size' => $fileSize,
            'content_type' => $this->getMimeType($fileName),
            'last_modified' => date('c'),
            'permissions' => 'RGDNVW',
            'is_directory' => false
        ];
        
        return true;
    }
    
    /**
     * Upload file from string content
     */
    public function uploadContent(string $content, string $remotePath, string $username = null): bool
    {
        $fileName = basename($remotePath);
        $dirPath = dirname($remotePath);
        
        if ($dirPath === '.') {
            $dirPath = '/';
        }
        
        // Ensure directory exists in mock
        if (!isset($this->mockFiles[$dirPath])) {
            $this->mockFiles[$dirPath] = [];
        }
        
        // Add file to mock directory
        $this->mockFiles[$dirPath][] = [
            'path' => $remotePath,
            'name' => $fileName,
            'size' => strlen($content),
            'content_type' => $this->getMimeType($fileName),
            'last_modified' => date('c'),
            'permissions' => 'RGDNVW',
            'is_directory' => false
        ];
        
        return true;
    }
    
    /**
     * Download a file from Nextcloud
     */
    public function downloadFile(string $remotePath, string $localPath = null, string $username = null): string|bool
    {
        $normalizedPath = rtrim($remotePath, '/');

        // Check if file exists in mock data
        $fileExists = false;
        foreach ($this->mockFiles as $dirPath => $items) {
            foreach ($items as $item) {
                if ($item['path'] === $normalizedPath && !$item['is_directory']) {
                    $fileExists = true;
                    break 2;
                }
            }
        }

        if (!$fileExists) {
            return false;
        }

        // Generate mock content based on file type
        $extension = strtolower(pathinfo($remotePath, PATHINFO_EXTENSION));
        $filename = basename($remotePath);

        switch ($extension) {
            case 'txt':
                $content = "This is a mock text file: $filename\n\nContent generated by MockNextcloudClient\nDate: " . date('Y-m-d H:i:s');
                break;
            case 'pdf':
                $content = "%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n178\n%%EOF";
                break;
            case 'json':
                $content = json_encode([
                    'filename' => $filename,
                    'type' => 'mock_file',
                    'generated_at' => date('c'),
                    'content' => 'This is mock JSON content'
                ], JSON_PRETTY_PRINT);
                break;
            default:
                $content = "Mock file content for: $filename\nGenerated at: " . date('Y-m-d H:i:s') . "\nFile type: $extension";
        }

        if ($localPath) {
            return file_put_contents($localPath, $content) !== false;
        }

        return $content;
    }
    
    /**
     * Create a directory
     */
    public function createDirectory(string $path, string $username = null): bool
    {
        $normalizedPath = rtrim($path, '/');
        if ($normalizedPath === '') {
            return false;
        }
        
        $parentPath = dirname($normalizedPath);
        if ($parentPath === '.') {
            $parentPath = '/';
        }
        
        // Ensure parent directory exists
        if (!isset($this->mockFiles[$parentPath])) {
            return false;
        }
        
        // Add directory to parent
        $this->mockFiles[$parentPath][] = [
            'path' => $normalizedPath,
            'name' => basename($normalizedPath),
            'size' => 0,
            'content_type' => '',
            'last_modified' => date('c'),
            'permissions' => 'RGDNVW',
            'is_directory' => true
        ];
        
        // Create empty directory entry
        $this->mockFiles[$normalizedPath] = [];
        
        return true;
    }
    
    /**
     * Delete a file or directory
     */
    public function delete(string $path, string $username = null): bool
    {
        $normalizedPath = rtrim($path, '/');
        $parentPath = dirname($normalizedPath);
        if ($parentPath === '.') {
            $parentPath = '/';
        }
        
        // Remove from parent directory
        if (isset($this->mockFiles[$parentPath])) {
            $this->mockFiles[$parentPath] = array_filter(
                $this->mockFiles[$parentPath],
                fn($item) => $item['path'] !== $normalizedPath
            );
        }
        
        // Remove directory entry if it's a directory
        if (isset($this->mockFiles[$normalizedPath])) {
            unset($this->mockFiles[$normalizedPath]);
        }
        
        return true;
    }
    
    /**
     * Move/rename a file or directory
     */
    public function move(string $fromPath, string $toPath, string $username = null): bool
    {
        $normalizedFromPath = rtrim($fromPath, '/');
        $normalizedToPath = rtrim($toPath, '/');

        // Find the item to move
        $itemToMove = null;
        $fromParentPath = dirname($normalizedFromPath);
        if ($fromParentPath === '.') {
            $fromParentPath = '/';
        }

        // Find and remove the item from its current location
        if (isset($this->mockFiles[$fromParentPath])) {
            foreach ($this->mockFiles[$fromParentPath] as $index => $item) {
                if ($item['path'] === $normalizedFromPath) {
                    $itemToMove = $item;
                    unset($this->mockFiles[$fromParentPath][$index]);
                    // Re-index array
                    $this->mockFiles[$fromParentPath] = array_values($this->mockFiles[$fromParentPath]);
                    break;
                }
            }
        }

        if (!$itemToMove) {
            return false; // Item not found
        }

        // Update item properties for new location
        $itemToMove['path'] = $normalizedToPath;
        $itemToMove['name'] = basename($normalizedToPath);

        // Add to new location
        $toParentPath = dirname($normalizedToPath);
        if ($toParentPath === '.') {
            $toParentPath = '/';
        }

        // Ensure parent directory exists
        if (!isset($this->mockFiles[$toParentPath])) {
            return false;
        }

        $this->mockFiles[$toParentPath][] = $itemToMove;

        // If it was a directory, update the directory entry key
        if ($itemToMove['is_directory'] && isset($this->mockFiles[$normalizedFromPath])) {
            $this->mockFiles[$normalizedToPath] = $this->mockFiles[$normalizedFromPath];
            unset($this->mockFiles[$normalizedFromPath]);
        }

        return true;
    }
    
    /**
     * Copy a file or directory
     */
    public function copy(string $fromPath, string $toPath, string $username = null): bool
    {
        // For mock, just simulate the operation
        return true;
    }
    
    /**
     * Get file/folder properties
     */
    public function getProperties(string $path, string $username = null): array|false
    {
        $normalizedPath = rtrim($path, '/');
        if ($normalizedPath === '') {
            $normalizedPath = '/';
        }
        
        // Search for the item in all directories
        foreach ($this->mockFiles as $dirPath => $items) {
            foreach ($items as $item) {
                if ($item['path'] === $normalizedPath) {
                    return $item;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Check if file or directory exists
     */
    public function exists(string $path, string $username = null): bool
    {
        return $this->getProperties($path, $username) !== false;
    }
    
    /**
     * Get direct download URL for a file
     */
    public function getDownloadUrl(string $path, string $username = null): string
    {
        return '/admin/storage/download?path=' . urlencode($path);
    }
    
    /**
     * Get MIME type for file
     */
    private function getMimeType(string $filename): string
    {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        $mimeTypes = [
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt' => 'application/vnd.ms-powerpoint',
            'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'txt' => 'text/plain',
            'html' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
            'json' => 'application/json',
            'xml' => 'application/xml',
            'zip' => 'application/zip',
            'mp4' => 'video/mp4',
            'mp3' => 'audio/mpeg'
        ];
        
        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }
}
