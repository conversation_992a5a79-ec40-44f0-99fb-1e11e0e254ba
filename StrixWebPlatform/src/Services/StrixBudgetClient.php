<?php

namespace Strix\ERP\Services;

use Exception;

class StrixBudgetClient
{
    private string $baseUrl;
    private ?string $token;
    private array $config;
    private bool $mockMode = false;

    public function __construct(string $baseUrl = null, string $token = null)
    {
        $this->config = [
            'timeout' => (int)($_ENV['STRIXBUDGET_TIMEOUT'] ?? 30),
            'verify_ssl' => filter_var($_ENV['STRIXBUDGET_VERIFY_SSL'] ?? true, FILTER_VALIDATE_BOOLEAN),
            'user_agent' => 'Strix-ERP/1.0'
        ];

        // Use provided URL or fallback to environment variable or default
        $this->baseUrl = $baseUrl ? rtrim($baseUrl, '/') :
                        rtrim($_ENV['STRIXBUDGET_API_URL'] ?? 'http://localhost:8000/api', '/');
        $this->token = $token;

        // Check if we should use mock mode
        $this->mockMode = $this->shouldUseMockMode();
    }

    /**
     * Check if we should use mock mode
     */
    private function shouldUseMockMode(): bool
    {
        // Check environment variable first
        $envMockMode = $_ENV['STRIXBUDGET_MOCK_MODE'] ?? 'false';
        if (filter_var($envMockMode, FILTER_VALIDATE_BOOLEAN)) {
            return true;
        }

        // If mock mode is explicitly disabled in env, don't use it
        if (strtolower($envMockMode) === 'false') {
            return false;
        }

        // Try to ping the API to see if it's available
        return !$this->isApiAvailable();
    }

    /**
     * Check if the API is available by making a simple request
     */
    private function isApiAvailable(): bool
    {
        try {
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $this->baseUrl . '/health',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 5, // Short timeout for availability check
                CURLOPT_CONNECTTIMEOUT => 3,
                CURLOPT_SSL_VERIFYPEER => $this->config['verify_ssl'],
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => 2,
                CURLOPT_NOBODY => true, // HEAD request
                CURLOPT_CUSTOMREQUEST => 'HEAD'
            ]);

            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            // Consider API available if we get any HTTP response (even 404)
            // This means the server is responding
            return $result !== false && empty($error) && $httpCode > 0;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Set authentication token
     */
    public function setToken(string $token): void
    {
        $this->token = $token;
    }

    /**
     * Check if client is in mock mode
     */
    public function isMockMode(): bool
    {
        return $this->mockMode;
    }

    /**
     * Force enable/disable mock mode
     */
    public function setMockMode(bool $mockMode): void
    {
        $this->mockMode = $mockMode;
        $this->log('info', 'Mock mode ' . ($mockMode ? 'enabled' : 'disabled'));
    }
    
    /**
     * Set base URL
     */
    public function setBaseUrl(string $baseUrl): void
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }
    
    /**
     * Login and get authentication token
     */
    public function login(string $email, string $password): array
    {
        if ($this->mockMode) {
            return $this->mockLogin($email, $password);
        }

        $response = $this->makeRequest('POST', '/auth/login', [
            'email' => $email,
            'password' => $password
        ]);

        if ($response['success'] && isset($response['data']['token'])) {
            $this->setToken($response['data']['token']);
        }

        return $response;
    }

    /**
     * Login with credentials and return token for storage
     */
    public function loginAndGetToken(string $email, string $password): array
    {
        $response = $this->login($email, $password);

        if ($response['success'] && isset($response['data']['token'])) {
            return [
                'success' => true,
                'token' => $response['data']['token'],
                'user' => $response['data']['user'] ?? null,
                'message' => 'Успешно влизане в StrixBudget'
            ];
        }

        return [
            'success' => false,
            'message' => $response['message'] ?? 'Грешка при влизане',
            'errors' => $response['errors'] ?? []
        ];
    }
    
    /**
     * Logout and invalidate token
     */
    public function logout(): array
    {
        $response = $this->makeRequest('POST', '/auth/logout');
        $this->token = null;
        return $response;
    }

    /**
     * Get current user information
     */
    public function getCurrentUser(): array
    {
        return $this->makeRequest('GET', '/auth/user');
    }

    /**
     * Get user statistics
     */
    public function getUserStatistics(): array
    {
        return $this->makeRequest('GET', '/auth/statistics');
    }

    /**
     * Update user profile
     */
    public function updateProfile(array $data): array
    {
        return $this->makeRequest('PUT', '/auth/profile', $data);
    }

    /**
     * Change user password
     */
    public function changePassword(array $data): array
    {
        return $this->makeRequest('PUT', '/auth/password', $data);
    }

    /**
     * Refresh authentication token
     */
    public function refreshToken(): array
    {
        return $this->makeRequest('POST', '/auth/refresh');
    }
    
    // Bank Accounts
    
    /**
     * Get all bank accounts
     */
    public function getBankAccounts(): array
    {
        return $this->makeRequest('GET', '/bank-accounts');
    }

    /**
     * Create new bank account
     */
    public function createBankAccount(array $data): array
    {
        return $this->makeRequest('POST', '/bank-accounts', $data);
    }

    /**
     * Get specific bank account
     */
    public function getBankAccount(int $id): array
    {
        return $this->makeRequest('GET', "/bank-accounts/{$id}");
    }

    /**
     * Update bank account
     */
    public function updateBankAccount(int $id, array $data): array
    {
        return $this->makeRequest('PUT', "/bank-accounts/{$id}", $data);
    }

    /**
     * Partially update bank account
     */
    public function patchBankAccount(int $id, array $data): array
    {
        return $this->makeRequest('PATCH', "/bank-accounts/{$id}", $data);
    }

    /**
     * Delete bank account
     */
    public function deleteBankAccount(int $id): array
    {
        return $this->makeRequest('DELETE', "/bank-accounts/{$id}");
    }

    /**
     * Get bank account statistics
     */
    public function getBankAccountStatistics(int $id): array
    {
        return $this->makeRequest('GET', "/bank-accounts/{$id}/statistics");
    }
    
    // Transactions
    
    /**
     * Get transactions with filters
     */
    public function getTransactions(array $filters = []): array
    {
        $queryString = http_build_query($filters);
        $endpoint = '/transactions' . ($queryString ? '?' . $queryString : '');
        return $this->makeRequest('GET', $endpoint);
    }

    /**
     * Create new transaction
     */
    public function createTransaction(array $data): array
    {
        return $this->makeRequest('POST', '/transactions', $data);
    }

    /**
     * Get specific transaction
     */
    public function getTransaction(int $id): array
    {
        return $this->makeRequest('GET', "/transactions/{$id}");
    }

    /**
     * Update transaction
     */
    public function updateTransaction(int $id, array $data): array
    {
        return $this->makeRequest('PUT', "/transactions/{$id}", $data);
    }

    /**
     * Partially update transaction
     */
    public function patchTransaction(int $id, array $data): array
    {
        return $this->makeRequest('PATCH', "/transactions/{$id}", $data);
    }

    /**
     * Delete transaction
     */
    public function deleteTransaction(int $id): array
    {
        return $this->makeRequest('DELETE', "/transactions/{$id}");
    }
    
    // Transfers
    
    /**
     * Get all transfers
     */
    public function getTransfers(): array
    {
        return $this->makeRequest('GET', '/transfers');
    }

    /**
     * Create new transfer
     */
    public function createTransfer(array $data): array
    {
        return $this->makeRequest('POST', '/transfers', $data);
    }

    /**
     * Get specific transfer
     */
    public function getTransfer(int $id): array
    {
        return $this->makeRequest('GET', "/transfers/{$id}");
    }

    /**
     * Update transfer
     */
    public function updateTransfer(int $id, array $data): array
    {
        return $this->makeRequest('PUT', "/transfers/{$id}", $data);
    }

    /**
     * Partially update transfer
     */
    public function patchTransfer(int $id, array $data): array
    {
        return $this->makeRequest('PATCH', "/transfers/{$id}", $data);
    }

    /**
     * Delete transfer
     */
    public function deleteTransfer(int $id): array
    {
        return $this->makeRequest('DELETE', "/transfers/{$id}");
    }
    
    // Counterparties
    
    /**
     * Get counterparties with filters
     */
    public function getCounterparties(array $filters = []): array
    {
        $queryString = http_build_query($filters);
        $endpoint = '/counterparties' . ($queryString ? '?' . $queryString : '');
        return $this->makeRequest('GET', $endpoint);
    }

    /**
     * Create new counterparty
     */
    public function createCounterparty(array $data): array
    {
        return $this->makeRequest('POST', '/counterparties', $data);
    }

    /**
     * Get specific counterparty
     */
    public function getCounterparty(int $id): array
    {
        return $this->makeRequest('GET', "/counterparties/{$id}");
    }

    /**
     * Update counterparty
     */
    public function updateCounterparty(int $id, array $data): array
    {
        return $this->makeRequest('PUT', "/counterparties/{$id}", $data);
    }

    /**
     * Partially update counterparty
     */
    public function patchCounterparty(int $id, array $data): array
    {
        return $this->makeRequest('PATCH', "/counterparties/{$id}", $data);
    }

    /**
     * Delete counterparty
     */
    public function deleteCounterparty(int $id): array
    {
        return $this->makeRequest('DELETE', "/counterparties/{$id}");
    }

    /**
     * Get counterparty statistics
     */
    public function getCounterpartyStatistics(int $id): array
    {
        return $this->makeRequest('GET', "/counterparties/{$id}/statistics");
    }

    /**
     * Get counterparty transactions
     */
    public function getCounterpartyTransactions(int $id): array
    {
        return $this->makeRequest('GET', "/counterparties/{$id}/transactions");
    }
    
    // Transaction Types
    
    /**
     * Get all transaction types
     */
    public function getTransactionTypes(): array
    {
        return $this->makeRequest('GET', '/transaction-types');
    }

    /**
     * Create new transaction type
     */
    public function createTransactionType(array $data): array
    {
        return $this->makeRequest('POST', '/transaction-types', $data);
    }

    /**
     * Get specific transaction type
     */
    public function getTransactionType(int $id): array
    {
        return $this->makeRequest('GET', "/transaction-types/{$id}");
    }

    /**
     * Update transaction type
     */
    public function updateTransactionType(int $id, array $data): array
    {
        return $this->makeRequest('PUT', "/transaction-types/{$id}", $data);
    }

    /**
     * Partially update transaction type
     */
    public function patchTransactionType(int $id, array $data): array
    {
        return $this->makeRequest('PATCH', "/transaction-types/{$id}", $data);
    }

    /**
     * Delete transaction type
     */
    public function deleteTransactionType(int $id): array
    {
        return $this->makeRequest('DELETE', "/transaction-types/{$id}");
    }

    /**
     * Get transaction type statistics
     */
    public function getTransactionTypeStatistics(int $id): array
    {
        return $this->makeRequest('GET', "/transaction-types/{$id}/statistics");
    }

    /**
     * Get transaction type transactions
     */
    public function getTransactionTypeTransactions(int $id): array
    {
        return $this->makeRequest('GET', "/transaction-types/{$id}/transactions");
    }

    /**
     * Test API connection
     */
    public function testConnection(): array
    {
        try {
            $this->log('info', 'Testing API connection...');

            // First check if API is available
            if (!$this->isApiAvailable()) {
                return [
                    'success' => false,
                    'message' => 'API сървърът не е достъпен на адрес: ' . $this->baseUrl,
                    'debug_info' => $this->getDebugInfo()
                ];
            }

            if (!$this->token) {
                return [
                    'success' => false,
                    'message' => 'Няма настроен токен за аутентикация',
                    'debug_info' => $this->getDebugInfo()
                ];
            }

            $response = $this->getCurrentUser();

            if ($response['success']) {
                $this->log('info', 'API connection test successful');
                return [
                    'success' => true,
                    'message' => 'Връзката със StrixBudget API е успешна' . ($this->mockMode ? ' (mock режим)' : ''),
                    'user' => $response['data'] ?? null,
                    'mock_mode' => $this->mockMode,
                    'api_url' => $this->baseUrl
                ];
            } else {
                $this->log('error', 'API connection test failed: ' . ($response['message'] ?? 'Unknown error'));
                return [
                    'success' => false,
                    'message' => 'Грешка при свързване: ' . ($response['message'] ?? 'Неизвестна грешка'),
                    'error_details' => $response,
                    'debug_info' => $this->getDebugInfo()
                ];
            }
        } catch (Exception $e) {
            $this->log('error', 'API connection test exception: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Грешка при тестване на връзката: ' . $e->getMessage(),
                'debug_info' => $this->getDebugInfo()
            ];
        }
    }

    /**
     * Make HTTP request to StrixBudget API
     */
    private function makeRequest(string $method, string $endpoint, array $data = null): array
    {
        if ($this->mockMode) {
            $this->log('debug', "Using mock mode for $method $endpoint");
            return $this->mockRequest($method, $endpoint, $data);
        }

        $url = $this->baseUrl . $endpoint;
        $this->log('debug', "Making real API request: $method $url");

        $ch = curl_init();

        $headers = [
            'Content-Type: application/json',
            'Accept: application/json',
            'User-Agent: ' . $this->config['user_agent']
        ];

        // Add authorization header if token is available
        if ($this->token) {
            $headers[] = 'Authorization: Bearer ' . $this->token;
        }

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->config['timeout'],
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => $this->config['verify_ssl'],
            CURLOPT_SSL_VERIFYHOST => $this->config['verify_ssl'] ? 2 : 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_USERAGENT => $this->config['user_agent']
        ]);

        // Add data for POST, PUT requests
        if ($data !== null && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            $jsonData = json_encode($data);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
            $this->log('debug', "Request data: " . $jsonData);
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $info = curl_getinfo($ch);

        curl_close($ch);

        if ($error) {
            $this->log('error', "cURL error: $error for $method $url");
            return [
                'success' => false,
                'message' => "Грешка при свързване с API: $error",
                'error_type' => 'connection_error'
            ];
        }

        if ($response === false) {
            $this->log('error', "Empty response for $method $url");
            return [
                'success' => false,
                'message' => "Празен отговор от API сървъра",
                'error_type' => 'empty_response'
            ];
        }

        $this->log('debug', "Response received: HTTP $httpCode, " . strlen($response) . " bytes");

        $decodedResponse = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->log('error', "JSON decode error for $method $url: " . json_last_error_msg() . ". Response: " . substr($response, 0, 200));
            return [
                'success' => false,
                'message' => "Невалиден JSON отговор от API: " . json_last_error_msg(),
                'error_type' => 'json_error',
                'raw_response' => substr($response, 0, 500)
            ];
        }

        if ($httpCode >= 400) {
            $this->log('error', "HTTP error $httpCode for $method $url. Response: " . substr($response, 0, 500));

            // Return structured error response
            return [
                'success' => false,
                'message' => $decodedResponse['message'] ?? "HTTP грешка $httpCode",
                'errors' => $decodedResponse['errors'] ?? [],
                'http_code' => $httpCode,
                'error_type' => 'http_error'
            ];
        }

        // Ensure response has success field
        if (!isset($decodedResponse['success'])) {
            $decodedResponse['success'] = true;
        }

        return $decodedResponse;
    }

    /**
     * Log messages for debugging
     */
    private function log(string $level, string $message): void
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] [StrixBudget] [$level] $message";

        // Log to error log
        error_log($logMessage);

        // Also log to a specific file for easier debugging
        $logFile = __DIR__ . '/../../logs/strixbudget.log';
        $logDir = dirname($logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        file_put_contents($logFile, $logMessage . PHP_EOL, FILE_APPEND | LOCK_EX);
    }

    /**
     * Get debug information about the client configuration
     */
    public function getDebugInfo(): array
    {
        return [
            'base_url' => $this->baseUrl,
            'has_token' => !empty($this->token),
            'token_preview' => $this->token ? substr($this->token, 0, 10) . '...' : 'not set',
            'timeout' => $this->config['timeout'],
            'verify_ssl' => $this->config['verify_ssl'],
            'user_agent' => $this->config['user_agent'],
            'mock_mode' => $this->mockMode,
            'api_available' => !$this->mockMode ? $this->isApiAvailable() : 'N/A (mock mode)',
            'environment' => [
                'STRIXBUDGET_API_URL' => $_ENV['STRIXBUDGET_API_URL'] ?? 'not set',
                'STRIXBUDGET_MOCK_MODE' => $_ENV['STRIXBUDGET_MOCK_MODE'] ?? 'not set',
                'STRIXBUDGET_TIMEOUT' => $_ENV['STRIXBUDGET_TIMEOUT'] ?? 'not set',
                'STRIXBUDGET_VERIFY_SSL' => $_ENV['STRIXBUDGET_VERIFY_SSL'] ?? 'not set'
            ]
        ];
    }

    // Mock methods for testing

    /**
     * Mock login for testing
     */
    private function mockLogin(string $email, string $password): array
    {
        // Simulate successful login for any email/password in mock mode
        $token = 'mock_token_' . time();
        $this->setToken($token);

        return [
            'success' => true,
            'data' => [
                'token' => $token,
                'user' => [
                    'id' => 1,
                    'name' => 'Mock User',
                    'email' => $email
                ]
            ],
            'message' => 'Успешно влизане (mock режим)'
        ];
    }



    /**
     * Mock API requests for testing
     */
    private function mockRequest(string $method, string $endpoint, array $data = null): array
    {
        // Mock responses for different endpoints
        switch ($endpoint) {
            case '/auth/user':
                return [
                    'success' => true,
                    'data' => [
                        'id' => 1,
                        'name' => 'Test User',
                        'email' => '<EMAIL>'
                    ]
                ];

            case '/auth/statistics':
                return [
                    'success' => true,
                    'data' => [
                        'total_accounts' => 3,
                        'total_balance' => 15000.50,
                        'monthly_income' => 5000.00,
                        'monthly_expenses' => 3500.00
                    ]
                ];

            case '/bank-accounts':
                return [
                    'success' => true,
                    'data' => [
                        [
                            'id' => 1,
                            'name' => 'Основна сметка',
                            'currency' => 'EUR',
                            'balance' => 10000.00,
                            'is_active' => true,
                            'is_default' => true
                        ],
                        [
                            'id' => 2,
                            'name' => 'Спестовна сметка',
                            'currency' => 'USD',
                            'balance' => 5000.50,
                            'is_active' => true,
                            'is_default' => false
                        ]
                    ]
                ];

            case '/transactions':
            case (preg_match('/^\/transactions\?/', $endpoint) ? $endpoint : null):
                return [
                    'success' => true,
                    'data' => [
                        [
                            'id' => 1,
                            'bank_account_id' => 1,
                            'type' => 'income',
                            'amount' => 2500.00,
                            'currency' => 'EUR',
                            'description' => 'Заплата',
                            'executed_at' => date('Y-m-d H:i:s')
                        ],
                        [
                            'id' => 2,
                            'bank_account_id' => 1,
                            'type' => 'expense',
                            'amount' => 150.00,
                            'currency' => 'EUR',
                            'description' => 'Пазаруване',
                            'executed_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
                        ]
                    ]
                ];

            case '/transfers':
                return [
                    'success' => true,
                    'data' => [
                        [
                            'id' => 1,
                            'from_account_id' => 1,
                            'to_account_id' => 2,
                            'from_account_name' => 'Основна сметка',
                            'to_account_name' => 'Спестовна сметка',
                            'amount' => 1000.00,
                            'currency' => 'EUR',
                            'description' => 'Месечно спестяване',
                            'date' => date('Y-m-d H:i:s', strtotime('-2 days')),
                            'created_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
                        ],
                        [
                            'id' => 2,
                            'from_account_id' => 2,
                            'to_account_id' => 1,
                            'from_account_name' => 'Спестовна сметка',
                            'to_account_name' => 'Основна сметка',
                            'amount' => 500.00,
                            'currency' => 'EUR',
                            'description' => 'Трансфер за разходи',
                            'date' => date('Y-m-d H:i:s', strtotime('-5 days')),
                            'created_at' => date('Y-m-d H:i:s', strtotime('-5 days'))
                        ],
                        [
                            'id' => 3,
                            'from_account_id' => 1,
                            'to_account_id' => 2,
                            'from_account_name' => 'Основна сметка',
                            'to_account_name' => 'Спестовна сметка',
                            'amount' => 750.00,
                            'currency' => 'EUR',
                            'description' => 'Допълнително спестяване',
                            'date' => date('Y-m-d H:i:s', strtotime('-1 week')),
                            'created_at' => date('Y-m-d H:i:s', strtotime('-1 week'))
                        ]
                    ]
                ];

            case '/counterparties':
            case (preg_match('/^\/counterparties\?/', $endpoint) ? $endpoint : null):
                return [
                    'success' => true,
                    'data' => [
                        [
                            'id' => 1,
                            'name' => 'ООД "Технологии България"',
                            'description' => 'IT услуги и консултации',
                            'email' => '<EMAIL>',
                            'phone' => '+359 2 123 4567',
                            'created_at' => date('Y-m-d H:i:s', strtotime('-30 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-5 days'))
                        ],
                        [
                            'id' => 2,
                            'name' => 'ЕООД "Софтуер Решения"',
                            'description' => 'Разработка на софтуер',
                            'email' => '<EMAIL>',
                            'phone' => '+359 32 987 6543',
                            'created_at' => date('Y-m-d H:i:s', strtotime('-25 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-3 days'))
                        ],
                        [
                            'id' => 3,
                            'name' => 'ЕТ "Иван Петров"',
                            'description' => 'Счетоводни услуги',
                            'email' => '<EMAIL>',
                            'phone' => '+359 88 123 4567',
                            'created_at' => date('Y-m-d H:i:s', strtotime('-20 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
                        ],
                        [
                            'id' => 4,
                            'name' => 'ООД "Маркетинг Плюс"',
                            'description' => 'Дигитален маркетинг и реклама',
                            'email' => '<EMAIL>',
                            'phone' => '+359 2 555 7890',
                            'created_at' => date('Y-m-d H:i:s', strtotime('-15 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
                        ],
                        [
                            'id' => 5,
                            'name' => 'ЕООД "Логистик Експрес"',
                            'description' => 'Транспорт и логистика',
                            'email' => '<EMAIL>',
                            'phone' => '+359 52 444 3333',
                            'created_at' => date('Y-m-d H:i:s', strtotime('-10 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-6 hours'))
                        ]
                    ]
                ];

            case '/transaction-types':
                return [
                    'success' => true,
                    'data' => [
                        [
                            'id' => 1,
                            'name' => 'Заплата',
                            'description' => 'Месечна заплата',
                            'type' => 'income',
                            'is_active' => true,
                            'created_at' => date('Y-m-d H:i:s', strtotime('-30 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-5 days'))
                        ],
                        [
                            'id' => 2,
                            'name' => 'Храна',
                            'description' => 'Разходи за храна и ресторанти',
                            'type' => 'expense',
                            'is_active' => true,
                            'created_at' => date('Y-m-d H:i:s', strtotime('-25 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-3 days'))
                        ],
                        [
                            'id' => 3,
                            'name' => 'Транспорт',
                            'description' => 'Разходи за транспорт и гориво',
                            'type' => 'expense',
                            'is_active' => true,
                            'created_at' => date('Y-m-d H:i:s', strtotime('-20 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
                        ],
                        [
                            'id' => 4,
                            'name' => 'Консултации',
                            'description' => 'Приходи от консултантски услуги',
                            'type' => 'income',
                            'is_active' => true,
                            'created_at' => date('Y-m-d H:i:s', strtotime('-15 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
                        ],
                        [
                            'id' => 5,
                            'name' => 'Комунални услуги',
                            'description' => 'Ток, вода, газ, интернет',
                            'type' => 'expense',
                            'is_active' => true,
                            'created_at' => date('Y-m-d H:i:s', strtotime('-10 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-6 hours'))
                        ]
                    ]
                ];

            default:
                // Handle specific counterparty requests
                if (preg_match('/^\/counterparties\/(\d+)$/', $endpoint, $matches)) {
                    $id = (int)$matches[1];
                    $counterparties = [
                        1 => [
                            'id' => 1,
                            'name' => 'ООД "Технологии България"',
                            'description' => 'IT услуги и консултации',
                            'email' => '<EMAIL>',
                            'phone' => '+359 2 123 4567',
                            'created_at' => date('Y-m-d H:i:s', strtotime('-30 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-5 days'))
                        ],
                        2 => [
                            'id' => 2,
                            'name' => 'ЕООД "Софтуер Решения"',
                            'description' => 'Разработка на софтуер',
                            'email' => '<EMAIL>',
                            'phone' => '+359 32 987 6543',
                            'created_at' => date('Y-m-d H:i:s', strtotime('-25 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-3 days'))
                        ],
                        3 => [
                            'id' => 3,
                            'name' => 'ЕТ "Иван Петров"',
                            'description' => 'Счетоводни услуги',
                            'email' => '<EMAIL>',
                            'phone' => '+359 88 123 4567',
                            'created_at' => date('Y-m-d H:i:s', strtotime('-20 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
                        ],
                        4 => [
                            'id' => 4,
                            'name' => 'ООД "Маркетинг Плюс"',
                            'description' => 'Дигитален маркетинг и реклама',
                            'email' => '<EMAIL>',
                            'phone' => '+359 2 555 7890',
                            'created_at' => date('Y-m-d H:i:s', strtotime('-15 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
                        ],
                        5 => [
                            'id' => 5,
                            'name' => 'ЕООД "Логистик Експрес"',
                            'description' => 'Транспорт и логистика',
                            'email' => '<EMAIL>',
                            'phone' => '+359 52 444 3333',
                            'created_at' => date('Y-m-d H:i:s', strtotime('-10 days')),
                            'updated_at' => date('Y-m-d H:i:s', strtotime('-6 hours'))
                        ]
                    ];

                    if (isset($counterparties[$id])) {
                        return [
                            'success' => true,
                            'data' => $counterparties[$id]
                        ];
                    } else {
                        return [
                            'success' => false,
                            'message' => 'Counterparty not found'
                        ];
                    }
                }

                return [
                    'success' => true,
                    'data' => [],
                    'message' => 'Mock response for ' . $endpoint
                ];
        }
    }
}
