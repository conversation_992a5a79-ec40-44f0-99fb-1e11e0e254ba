<?php
ob_start();
?>

<div class="login-container">
    <div class="login-card">
        <h2>Вход в системата</h2>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-error">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($errors) && !empty($errors)): ?>
            <div class="alert alert-error">
                <ul style="margin: 0; padding-left: 20px;">
                    <?php foreach ($errors as $field => $fieldErrors): ?>
                        <?php foreach ($fieldErrors as $error): ?>
                            <li><?= htmlspecialchars($error) ?></li>
                        <?php endforeach; ?>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="/login" data-validate>
            <input type="hidden" name="_token" value="<?= $csrf_token ?>">
            
            <div class="form-group">
                <label for="username">Потребителско име или имейл:</label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    class="form-control" 
                    value="<?= htmlspecialchars($old_input['username'] ?? '') ?>"
                    required
                    autofocus
                >
            </div>
            
            <div class="form-group">
                <label for="password">Парола:</label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    class="form-control" 
                    required
                    data-min-length="1"
                >
            </div>
            
            <div class="form-group">
                <label style="display: flex; align-items: center; font-weight: normal;">
                    <input type="checkbox" name="remember" value="1" style="margin-right: 8px;">
                    Запомни ме
                </label>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary" style="width: 100%; padding: 12px;">
                    Вход
                </button>
            </div>
        </form>
        
        <div style="text-align: center; margin-top: 20px; color: #7f8c8d; font-size: 14px;">
            <p>Тестов акаунт:</p>
            <p><strong>Потребител:</strong> admin</p>
            <p><strong>Парола:</strong> admin123</p>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
$showSidebar = false;
include __DIR__ . '/../layouts/app.php';
?>
