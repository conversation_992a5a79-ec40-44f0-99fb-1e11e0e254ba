<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
$user = $app->getCurrentUser();
$currentPath = $_SERVER['REQUEST_URI'];
?>

<div class="sidebar">
    <div class="logo">
        <h2>Strix ERP</h2>
    </div>
    
    <nav>
        <ul>
            <li>
                <a href="/admin" class="<?= $currentPath === '/admin' ? 'active' : '' ?>">
                    📊 Дашборд
                </a>
            </li>
            
            <?php if ($app->hasPermission('users.view')): ?>
            <li>
                <a href="/admin/users" class="<?= strpos($currentPath, '/admin/users') === 0 ? 'active' : '' ?>">
                    👥 Потребители
                </a>
            </li>
            <?php endif; ?>
            
            <?php if ($app->hasPermission('groups.view')): ?>
            <li>
                <a href="/admin/groups" class="<?= strpos($currentPath, '/admin/groups') === 0 ? 'active' : '' ?>">
                    🏷️ Групи
                </a>
            </li>
            <?php endif; ?>

            <?php if ($app->hasPermission('tasks.view')): ?>
            <li>
                <a href="/admin/tasks" class="<?= strpos($currentPath, '/admin/tasks') === 0 ? 'active' : '' ?>">
                    📋 Задачи
                </a>
            </li>
            <?php endif; ?>

            <?php if ($app->hasPermission('storage.view')): ?>
            <li>
                <a href="/admin/storage" class="<?= strpos($currentPath, '/admin/storage') === 0 ? 'active' : '' ?>">
                    💾 Storage/Disk
                </a>
            </li>
            <?php endif; ?>

            <?php if ($app->hasPermission('strixbudget.view')): ?>
            <li>
                <a href="/admin/strixbudget" class="<?= strpos($currentPath, '/admin/strixbudget') === 0 ? 'active' : '' ?>">
                    💰 StrixBudget
                </a>
            </li>
            <?php endif; ?>

            <?php if ($app->hasPermission('reports.view')): ?>
            <li>
                <a href="/admin/reports" class="<?= strpos($currentPath, '/admin/reports') === 0 ? 'active' : '' ?>">
                    📈 Отчети
                </a>
            </li>
            <?php endif; ?>
            
            <?php if ($app->hasPermission('admin.system_settings')): ?>
            <li>
                <a href="/admin/settings" class="<?= strpos($currentPath, '/admin/settings') === 0 ? 'active' : '' ?>">
                    ⚙️ Настройки
                </a>
            </li>
            <?php endif; ?>
            
            <li style="margin-top: 20px; border-top: 1px solid #34495e; padding-top: 20px;">
                <a href="/admin/profile">
                    👤 <?= htmlspecialchars($user['full_name']) ?>
                </a>
            </li>
            
            <li>
                <form method="POST" action="/logout" style="margin: 0;">
                    <input type="hidden" name="_token" value="<?= $_SESSION['csrf_token'] ?? '' ?>">
                    <button type="submit" style="background: none; border: none; color: #bdc3c7; padding: 12px 20px; width: 100%; text-align: left; cursor: pointer; transition: all 0.3s ease;">
                        🚪 Изход
                    </button>
                </form>
            </li>
        </ul>
    </nav>
</div>
