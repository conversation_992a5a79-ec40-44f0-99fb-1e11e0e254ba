<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
$currentUser = $app->getCurrentUser();
ob_start();

// Get task details
$creator = $task->getCreator();
$primaryAssignee = $task->getPrimaryAssignee();
$taskType = $task->getTaskType();
$taskStatus = $task->getTaskStatus();
?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                <h3><?= htmlspecialchars($task->title) ?></h3>
                <div>
                    <?php if ($app->hasPermission('tasks.edit')): ?>
                        <a href="/admin/tasks/<?= $task->id ?>/edit" class="btn btn-warning btn-sm">
                            ✏️ Редактирай
                        </a>
                    <?php endif; ?>
                    
                    <?php if ($app->hasPermission('tasks.delete')): ?>
                        <form method="POST" action="/admin/tasks/<?= $task->id ?>" style="display: inline;">
                            <input type="hidden" name="_method" value="DELETE">
                            <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                            <button 
                                type="submit" 
                                class="btn btn-danger btn-sm"
                                onclick="return confirm('Сигурни ли сте, че искате да изтриете тази задача?')"
                            >
                                🗑️ Изтрий
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="card-body">
                <?php if ($task->description): ?>
                    <div class="form-group">
                        <label><strong>Описание:</strong></label>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; border-left: 4px solid #007bff;">
                            <?= nl2br(htmlspecialchars($task->description)) ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>Тип:</strong></label>
                            <div>
                                <?php if ($taskType): ?>
                                    <span style="background: <?= $taskType->color ?>; color: white; padding: 4px 8px; border-radius: 4px;">
                                        <?= htmlspecialchars($taskType->name) ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">Неизвестен</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>Статус:</strong></label>
                            <div>
                                <?php if ($taskStatus): ?>
                                    <span style="background: <?= $taskStatus->color ?>; color: white; padding: 4px 8px; border-radius: 4px;">
                                        <?= htmlspecialchars($taskStatus->name) ?>
                                    </span>
                                <?php else: ?>
                                    <span class="text-muted">Неизвестен</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>Приоритет:</strong></label>
                            <div>
                                <?php
                                $priorityLabels = [
                                    'low' => 'Нисък',
                                    'normal' => 'Нормален', 
                                    'high' => 'Висок',
                                    'urgent' => 'Спешно'
                                ];
                                $priorityColors = [
                                    'low' => '#95a5a6',
                                    'normal' => '#3498db',
                                    'high' => '#f39c12', 
                                    'urgent' => '#e74c3c'
                                ];
                                ?>
                                <span style="color: <?= $priorityColors[$task->priority] ?? '#95a5a6' ?>; font-weight: bold;">
                                    <?= $priorityLabels[$task->priority] ?? 'Неизвестен' ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>Прогрес:</strong></label>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div style="width: 100px; height: 10px; background: #e9ecef; border-radius: 5px; overflow: hidden;">
                                    <div style="width: <?= $task->progress ?>%; height: 100%; background: #28a745; transition: width 0.3s;"></div>
                                </div>
                                <span><?= $task->progress ?>%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>Създател:</strong></label>
                            <div>
                                <?php if ($creator): ?>
                                    <?= htmlspecialchars($creator->getFullName()) ?>
                                <?php else: ?>
                                    <span class="text-muted">Неизвестен</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>Основен изпълнител:</strong></label>
                            <div>
                                <?php if ($primaryAssignee): ?>
                                    <?= htmlspecialchars($primaryAssignee->getFullName()) ?>
                                <?php else: ?>
                                    <span class="text-muted">Неназначен</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($assignees)): ?>
                    <div class="form-group">
                        <label><strong>Допълнителни изпълнители:</strong></label>
                        <div>
                            <?php foreach ($assignees as $assignee): ?>
                                <span class="badge badge-secondary" style="margin-right: 5px; margin-bottom: 5px;">
                                    <?php if ($assignee['type'] === 'user'): ?>
                                        👤 <?= htmlspecialchars($assignee['name']) ?>
                                    <?php else: ?>
                                        👥 <?= htmlspecialchars($assignee['name']) ?>
                                    <?php endif; ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>Начална дата:</strong></label>
                            <div>
                                <?= $task->start_date ? date('d.m.Y', strtotime($task->start_date)) : 'Не е зададена' ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>Краен срок:</strong></label>
                            <div>
                                <?php if ($task->due_date): ?>
                                    <?php
                                    $dueDate = strtotime($task->due_date);
                                    $today = strtotime(date('Y-m-d'));
                                    $isOverdue = $dueDate < $today;
                                    $daysLeft = ceil(($dueDate - $today) / (24 * 60 * 60));
                                    ?>
                                    <span style="color: <?= $isOverdue ? '#e74c3c' : ($daysLeft <= 3 ? '#f39c12' : '#2c3e50') ?>;">
                                        <?= date('d.m.Y', $dueDate) ?>
                                        <?php if ($isOverdue): ?>
                                            (Просрочена)
                                        <?php elseif ($daysLeft <= 7): ?>
                                            (<?= $daysLeft ?> дни)
                                        <?php endif; ?>
                                    </span>
                                <?php else: ?>
                                    Не е зададен
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>Очаквани часове:</strong></label>
                            <div>
                                <?= $task->estimated_hours ? number_format($task->estimated_hours, 1) . ' ч.' : 'Не е зададено' ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>Отработени часове:</strong></label>
                            <div>
                                <?= $task->actual_hours ? number_format($task->actual_hours, 1) . ' ч.' : 'Не е зададено' ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>Създадена:</strong></label>
                            <div>
                                <?= date('d.m.Y H:i', strtotime($task->created_at)) ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="form-group">
                            <label><strong>Последна промяна:</strong></label>
                            <div>
                                <?= date('d.m.Y H:i', strtotime($task->updated_at)) ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Attachments -->
        <?php if (!empty($attachments) || $app->hasPermission('tasks.attach')): ?>
            <div class="card mb-3">
                <div class="card-header">
                    <h5>Прикачени файлове</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($attachments)): ?>
                        <p class="text-muted">Няма прикачени файлове</p>
                    <?php else: ?>
                        <?php foreach ($attachments as $attachment): ?>
                            <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                                <a href="/admin/tasks/<?= $task->id ?>/attachments/<?= $attachment['id'] ?>/download">
                                    📎 <?= htmlspecialchars($attachment['filename']) ?>
                                </a>
                                <br>
                                <small class="text-muted">
                                    <?= number_format($attachment['filesize'] / 1024, 1) ?> KB
                                    - <?= date('d.m.Y H:i', strtotime($attachment['created_at'])) ?>
                                </small>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    
                    <?php if ($app->hasPermission('tasks.attach')): ?>
                        <form method="POST" action="/admin/tasks/<?= $task->id ?>/attachments" enctype="multipart/form-data" style="margin-top: 15px;">
                            <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                            <input type="file" name="attachment" class="form-control-file" style="margin-bottom: 10px;">
                            <button type="submit" class="btn btn-primary btn-sm">Прикачи файл</button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Comments -->
        <div class="card">
            <div class="card-header">
                <h5>Коментари</h5>
            </div>
            <div class="card-body">
                <?php if (empty($comments)): ?>
                    <p class="text-muted">Няма коментари</p>
                <?php else: ?>
                    <div style="max-height: 300px; overflow-y: auto;">
                        <?php foreach ($comments as $comment): ?>
                            <div style="border-bottom: 1px solid #eee; padding: 10px 0;">
                                <strong><?= htmlspecialchars($comment['author_name']) ?></strong>
                                <small class="text-muted">- <?= date('d.m.Y H:i', strtotime($comment['created_at'])) ?></small>
                                <div style="margin-top: 5px;">
                                    <?= nl2br(htmlspecialchars($comment['content'])) ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($app->hasPermission('tasks.comment')): ?>
                    <form method="POST" action="/admin/tasks/<?= $task->id ?>/comments" style="margin-top: 15px;">
                        <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                        <textarea 
                            name="content" 
                            class="form-control" 
                            rows="3" 
                            placeholder="Добавете коментар..."
                            required
                        ></textarea>
                        <button type="submit" class="btn btn-primary btn-sm" style="margin-top: 10px;">
                            Добави коментар
                        </button>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div style="margin-top: 20px;">
    <a href="/admin/tasks" class="btn btn-secondary">
        ↩️ Обратно към задачите
    </a>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
