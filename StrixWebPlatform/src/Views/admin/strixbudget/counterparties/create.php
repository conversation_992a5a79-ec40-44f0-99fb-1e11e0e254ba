<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h3>➕ Създаване на контрагент</h3>
        <div style="display: flex; gap: 10px; align-items: center;">
            <a href="/admin/strixbudget/counterparties" class="btn btn-outline-secondary">
                ← Назад към списъка
            </a>
        </div>
    </div>
    <div class="card-body">

            <?php if (isset($error)): ?>
                <div class="alert alert-danger" role="alert">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if (isset($errors) && !empty($errors)): ?>
                <div class="alert alert-danger" role="alert">
                    <ul class="mb-0">
                        <?php foreach ($errors as $field => $error): ?>
                            <li><?= htmlspecialchars($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Данни за контрагента</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="/admin/strixbudget/counterparties">
                                <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="name" class="form-label">Име *</label>
                                            <input type="text" 
                                                   class="form-control <?= isset($errors['name']) ? 'is-invalid' : '' ?>" 
                                                   id="name" 
                                                   name="name" 
                                                   value="<?= htmlspecialchars($old_input['name'] ?? '') ?>"
                                                   placeholder="Име на контрагента"
                                                   required>
                                            <div class="form-text">Въведете пълното име на контрагента</div>
                                            <?php if (isset($errors['name'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['name']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="type" class="form-label">Тип *</label>
                                            <select class="form-select <?= isset($errors['type']) ? 'is-invalid' : '' ?>" 
                                                    id="type" 
                                                    name="type" 
                                                    required>
                                                <option value="">Изберете тип</option>
                                                <option value="client" <?= ($old_input['type'] ?? '') === 'client' ? 'selected' : '' ?>>
                                                    👔 Клиент
                                                </option>
                                                <option value="supplier" <?= ($old_input['type'] ?? '') === 'supplier' ? 'selected' : '' ?>>
                                                    🚚 Доставчик
                                                </option>
                                                <option value="employee" <?= ($old_input['type'] ?? '') === 'employee' ? 'selected' : '' ?>>
                                                    👥 Служител
                                                </option>
                                                <option value="other" <?= ($old_input['type'] ?? '') === 'other' ? 'selected' : '' ?>>
                                                    📋 Друго
                                                </option>
                                            </select>
                                            <?php if (isset($errors['type'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['type']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="company" class="form-label">Фирма</label>
                                    <input type="text" 
                                           class="form-control <?= isset($errors['company']) ? 'is-invalid' : '' ?>" 
                                           id="company" 
                                           name="company" 
                                           value="<?= htmlspecialchars($old_input['company'] ?? '') ?>"
                                           placeholder="Име на фирмата (ако е приложимо)">
                                    <div class="form-text">Опционално поле за име на фирма или организация</div>
                                    <?php if (isset($errors['company'])): ?>
                                        <div class="invalid-feedback">
                                            <?= htmlspecialchars($errors['company']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="email" class="form-label">Email</label>
                                            <input type="email" 
                                                   class="form-control <?= isset($errors['email']) ? 'is-invalid' : '' ?>" 
                                                   id="email" 
                                                   name="email" 
                                                   value="<?= htmlspecialchars($old_input['email'] ?? '') ?>"
                                                   placeholder="<EMAIL>">
                                            <div class="form-text">Email адрес за контакт</div>
                                            <?php if (isset($errors['email'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['email']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="phone" class="form-label">Телефон</label>
                                            <input type="tel" 
                                                   class="form-control <?= isset($errors['phone']) ? 'is-invalid' : '' ?>" 
                                                   id="phone" 
                                                   name="phone" 
                                                   value="<?= htmlspecialchars($old_input['phone'] ?? '') ?>"
                                                   placeholder="+359 888 123 456">
                                            <div class="form-text">Телефонен номер за контакт</div>
                                            <?php if (isset($errors['phone'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['phone']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="address" class="form-label">Адрес</label>
                                    <textarea class="form-control <?= isset($errors['address']) ? 'is-invalid' : '' ?>" 
                                              id="address" 
                                              name="address" 
                                              rows="2"
                                              placeholder="Пълен адрес на контрагента..."><?= htmlspecialchars($old_input['address'] ?? '') ?></textarea>
                                    <div class="form-text">Пълен адрес включително град и пощенски код</div>
                                    <?php if (isset($errors['address'])): ?>
                                        <div class="invalid-feedback">
                                            <?= htmlspecialchars($errors['address']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="tax_number" class="form-label">ЕИК/БУЛСТАТ</label>
                                            <input type="text" 
                                                   class="form-control <?= isset($errors['tax_number']) ? 'is-invalid' : '' ?>" 
                                                   id="tax_number" 
                                                   name="tax_number" 
                                                   value="<?= htmlspecialchars($old_input['tax_number'] ?? '') ?>"
                                                   placeholder="*********">
                                            <div class="form-text">ЕИК за фирми или БУЛСТАТ за организации</div>
                                            <?php if (isset($errors['tax_number'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['tax_number']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="vat_number" class="form-label">ДДС номер</label>
                                            <input type="text" 
                                                   class="form-control <?= isset($errors['vat_number']) ? 'is-invalid' : '' ?>" 
                                                   id="vat_number" 
                                                   name="vat_number" 
                                                   value="<?= htmlspecialchars($old_input['vat_number'] ?? '') ?>"
                                                   placeholder="BG*********">
                                            <div class="form-text">ДДС номер (ако е приложимо)</div>
                                            <?php if (isset($errors['vat_number'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['vat_number']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="description" class="form-label">Описание</label>
                                    <textarea class="form-control <?= isset($errors['description']) ? 'is-invalid' : '' ?>" 
                                              id="description" 
                                              name="description" 
                                              rows="3"
                                              placeholder="Допълнителна информация за контрагента..."><?= htmlspecialchars($old_input['description'] ?? '') ?></textarea>
                                    <div class="form-text">Опционално поле за допълнителна информация</div>
                                    <?php if (isset($errors['description'])): ?>
                                        <div class="invalid-feedback">
                                            <?= htmlspecialchars($errors['description']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="is_active" 
                                               name="is_active" 
                                               value="1"
                                               <?= ($old_input['is_active'] ?? true) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="is_active">
                                            Активен контрагент
                                        </label>
                                    </div>
                                    <div class="form-text">Активните контрагенти се показват в списъците за избор</div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="/admin/strixbudget/counterparties" class="btn btn-secondary me-md-2">
                                        Отказ
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        💾 Създаване на контрагент
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-info">Помощ</h6>
                        </div>
                        <div class="card-body">
                            <h6>Типове контрагенти:</h6>
                            <ul class="small">
                                <li><strong>Клиент</strong> - лица/фирми, които ви плащат</li>
                                <li><strong>Доставчик</strong> - лица/фирми, на които плащате</li>
                                <li><strong>Служител</strong> - служители на фирмата</li>
                                <li><strong>Друго</strong> - други видове контрагенти</li>
                            </ul>

                            <hr>

                            <h6>Съвети:</h6>
                            <ul class="small">
                                <li>Използвайте описателни имена</li>
                                <li>Добавете контактна информация</li>
                                <li>ЕИК/БУЛСТАТ са полезни за отчети</li>
                                <li>Деактивирайте неизползвани контрагенти</li>
                            </ul>

                            <hr>

                            <h6>Бързи действия:</h6>
                            <div class="d-grid gap-2">
                                <a href="/admin/strixbudget/counterparties" class="btn btn-sm btn-outline-primary">
                                    📋 Всички контрагенти
                                </a>
                                <a href="/admin/strixbudget/transactions/create" class="btn btn-sm btn-outline-success">
                                    ➕ Нова транзакция
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const type = document.getElementById('type').value;

            if (!name) {
                alert('Моля въведете име на контрагента');
                e.preventDefault();
                return;
            }

            if (!type) {
                alert('Моля изберете тип контрагент');
                e.preventDefault();
                return;
            }
        });
    }

    // Auto-format phone number
    const phoneInput = document.getElementById('phone');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.startsWith('359')) {
                value = '+' + value;
            } else if (value.startsWith('0')) {
                value = '+359' + value.substring(1);
            }
            e.target.value = value;
        });
    }

    // Auto-format tax number
    const taxNumberInput = document.getElementById('tax_number');
    if (taxNumberInput) {
        taxNumberInput.addEventListener('input', function(e) {
            // Remove non-digits
            e.target.value = e.target.value.replace(/\D/g, '');
        });
    }
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../../layouts/app.php';
?>
