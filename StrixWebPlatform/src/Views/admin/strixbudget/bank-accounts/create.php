<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h3>➕ Създаване на банкова сметка</h3>
        <div style="display: flex; gap: 10px; align-items: center;">
            <a href="/admin/strixbudget/bank-accounts" class="btn btn-outline-secondary">
                ← Назад към списъка
            </a>
        </div>
    </div>
    <div class="card-body">

            <?php if (isset($error)): ?>
                <div class="alert alert-danger" role="alert">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if (isset($errors) && !empty($errors)): ?>
                <div class="alert alert-danger" role="alert">
                    <ul class="mb-0">
                        <?php foreach ($errors as $field => $error): ?>
                            <li><?= htmlspecialchars($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Данни за банковата сметка</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="/admin/strixbudget/bank-accounts">
                                <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                                
                                <div class="form-group mb-3">
                                    <label for="name" class="form-label">Име на сметката *</label>
                                    <input type="text" 
                                           class="form-control <?= isset($errors['name']) ? 'is-invalid' : '' ?>" 
                                           id="name" 
                                           name="name" 
                                           value="<?= htmlspecialchars($old_input['name'] ?? '') ?>"
                                           placeholder="Например: Основна сметка, Спестовна сметка"
                                           required>
                                    <div class="form-text">Въведете описателно име за банковата сметка</div>
                                    <?php if (isset($errors['name'])): ?>
                                        <div class="invalid-feedback">
                                            <?= htmlspecialchars($errors['name']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="currency" class="form-label">Валута *</label>
                                            <select class="form-select <?= isset($errors['currency']) ? 'is-invalid' : '' ?>" 
                                                    id="currency" 
                                                    name="currency" 
                                                    required>
                                                <option value="">Изберете валута</option>
                                                <?php foreach ($currencies as $code => $name): ?>
                                                    <option value="<?= $code ?>" 
                                                            <?= ($old_input['currency'] ?? '') === $code ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($name) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <?php if (isset($errors['currency'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['currency']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="balance" class="form-label">Начален баланс *</label>
                                            <div class="input-group">
                                                <input type="number" 
                                                       class="form-control <?= isset($errors['balance']) ? 'is-invalid' : '' ?>" 
                                                       id="balance" 
                                                       name="balance" 
                                                       value="<?= htmlspecialchars($old_input['balance'] ?? '0.00') ?>"
                                                       step="0.01"
                                                       min="0"
                                                       placeholder="0.00"
                                                       required>
                                                <span class="input-group-text" id="currency-symbol">EUR</span>
                                            </div>
                                            <div class="form-text">Въведете текущия баланс на сметката</div>
                                            <?php if (isset($errors['balance'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['balance']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" 
                                                       type="checkbox" 
                                                       id="is_active" 
                                                       name="is_active" 
                                                       value="1"
                                                       <?= ($old_input['is_active'] ?? true) ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="is_active">
                                                    Активна сметка
                                                </label>
                                            </div>
                                            <div class="form-text">Активните сметки се показват в списъците за избор</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" 
                                                       type="checkbox" 
                                                       id="is_default" 
                                                       name="is_default" 
                                                       value="1"
                                                       <?= ($old_input['is_default'] ?? false) ? 'checked' : '' ?>>
                                                <label class="form-check-label" for="is_default">
                                                    Сметка по подразбиране
                                                </label>
                                            </div>
                                            <div class="form-text">Сметката по подразбиране се избира автоматично в формите</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="/admin/strixbudget/bank-accounts" class="btn btn-secondary me-md-2">
                                        Отказ
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        💾 Създаване на сметка
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-info">Помощ</h6>
                        </div>
                        <div class="card-body">
                            <h6>Съвети за създаване на сметка:</h6>
                            <ul class="small">
                                <li>Използвайте описателни имена като "Основна сметка", "Спестовна сметка"</li>
                                <li>Изберете правилната валута за сметката</li>
                                <li>Въведете точния текущ баланс</li>
                                <li>Активирайте сметката, за да я използвате</li>
                                <li>Само една сметка може да бъде по подразбиране</li>
                            </ul>

                            <hr>

                            <h6>Поддържани валути:</h6>
                            <ul class="small">
                                <?php foreach ($currencies as $code => $name): ?>
                                    <li><?= htmlspecialchars($name) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update currency symbol when currency changes
    const currencySelect = document.getElementById('currency');
    const currencySymbol = document.getElementById('currency-symbol');
    
    if (currencySelect && currencySymbol) {
        currencySelect.addEventListener('change', function() {
            currencySymbol.textContent = this.value || 'EUR';
        });
        
        // Set initial value
        if (currencySelect.value) {
            currencySymbol.textContent = currencySelect.value;
        }
    }

    // Handle default checkbox - only one can be default
    const isDefaultCheckbox = document.getElementById('is_default');
    if (isDefaultCheckbox) {
        isDefaultCheckbox.addEventListener('change', function() {
            if (this.checked) {
                // Show warning that this will become the new default
                if (!confirm('Тази сметка ще стане новата сметка по подразбиране. Продължавате ли?')) {
                    this.checked = false;
                }
            }
        });
    }

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const name = document.getElementById('name').value.trim();
            const currency = document.getElementById('currency').value;
            const balance = document.getElementById('balance').value;

            if (!name) {
                alert('Моля въведете име на сметката');
                e.preventDefault();
                return;
            }

            if (!currency) {
                alert('Моля изберете валута');
                e.preventDefault();
                return;
            }

            if (!balance || isNaN(parseFloat(balance))) {
                alert('Моля въведете валиден баланс');
                e.preventDefault();
                return;
            }
        });
    }
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../../layouts/app.php';
?>
