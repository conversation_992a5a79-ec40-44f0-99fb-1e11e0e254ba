<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h3>🏦 Банкови сметки</h3>
        <div style="display: flex; gap: 10px; align-items: center;">
            <a href="/admin/strixbudget" class="btn btn-outline-secondary">
                ← Dashboard
            </a>
            <a href="/admin/strixbudget/bank-accounts/create" class="btn btn-primary">
                ➕ Нова сметка
            </a>
        </div>
    </div>
    <div class="card-body">

            <?php if (isset($error)): ?>
                <div class="alert alert-danger" role="alert">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

        <?php if (empty($accounts)): ?>
            <div class="text-center py-5">
                <h4 class="text-muted">Няма банкови сметки</h4>
                <p class="text-muted">Създайте първата си банкова сметка, за да започнете да следите финансите си.</p>
                <a href="/admin/strixbudget/bank-accounts/create" class="btn btn-primary">
                    ➕ Създаване на първа сметка
                </a>
            </div>
        <?php else: ?>
            <div class="mb-4">
                <h6 class="mb-3 font-weight-bold text-primary">
                    Всички банкови сметки (<?= count($accounts) ?>)
                </h6>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="accountsTable">
                                <thead>
                                    <tr>
                                        <th>Име</th>
                                        <th>Валута</th>
                                        <th>Баланс</th>
                                        <th>Статус</th>
                                        <th>По подразбиране</th>
                                        <th>Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($accounts as $account): ?>
                                    <tr>
                                        <td>
                                            <strong><?= htmlspecialchars($account->name ?? '') ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?= htmlspecialchars($account->currency ?? '') ?></span>
                                        </td>
                                        <td class="text-end">
                                            <strong class="<?= ($account->balance ?? 0) >= 0 ? 'text-success' : 'text-danger' ?>">
                                                <?= $account->getFormattedBalance() ?>
                                            </strong>
                                        </td>
                                        <td>
                                            <?php if ($account->isActive()): ?>
                                                <span class="badge bg-success">Активна</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Неактивна</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($account->isDefault()): ?>
                                                <span class="badge bg-primary">Да</span>
                                            <?php else: ?>
                                                <span class="text-muted">Не</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="/admin/strixbudget/bank-accounts/<?= $account->id ?>" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   title="Преглед">
                                                    👁️
                                                </a>
                                                <a href="/admin/strixbudget/bank-accounts/<?= $account->id ?>/edit" 
                                                   class="btn btn-sm btn-outline-secondary" 
                                                   title="Редактиране">
                                                    ✏️
                                                </a>
                                                <button type="button" 
                                                        class="btn btn-sm btn-outline-danger" 
                                                        onclick="deleteAccount(<?= $account->id ?>, '<?= htmlspecialchars($account->name ?? '', ENT_QUOTES) ?>')"
                                                        title="Изтриване">
                                                    🗑️
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                </div>
            </div>

            <!-- Summary Cards -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            Общо сметки
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?= count($accounts) ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-university fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            Активни сметки
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php 
                                            $activeCount = 0;
                                            foreach ($accounts as $account) {
                                                if ($account->isActive()) $activeCount++;
                                            }
                                            echo $activeCount;
                                            ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            Общ баланс (EUR)
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php 
                                            $totalBalance = 0;
                                            foreach ($accounts as $account) {
                                                if ($account->currency === 'EUR') {
                                                    $totalBalance += (float) ($account->balance ?? 0);
                                                }
                                            }
                                            echo number_format($totalBalance, 2);
                                            ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-euro-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
function deleteAccount(id, name) {
    if (confirm(`Сигурни ли сте, че искате да изтриете банковата сметка "${name}"?\n\nТова действие не може да бъде отменено.`)) {
        fetch(`/admin/strixbudget/bank-accounts/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                _token: '<?= $_SESSION['csrf_token'] ?? '' ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('✅ ' + data.message);
                location.reload();
            } else {
                alert('❌ ' + (data.error || 'Грешка при изтриване'));
            }
        })
        .catch(error => {
            alert('❌ Грешка при изтриване: ' + error.message);
        });
    }
}

// Initialize DataTable if available
document.addEventListener('DOMContentLoaded', function() {
    if (typeof $ !== 'undefined' && $.fn.DataTable) {
        $('#accountsTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Bulgarian.json"
            },
            "pageLength": 25,
            "order": [[0, "asc"]],
            "columnDefs": [
                { "orderable": false, "targets": [5] }
            ]
        });
    }
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../../layouts/app.php';
?>
