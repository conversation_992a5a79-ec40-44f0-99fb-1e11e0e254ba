<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h3>➕ Създаване на тип транзакция</h3>
        <div style="display: flex; gap: 10px; align-items: center;">
            <a href="/admin/strixbudget/transaction-types" class="btn btn-outline-secondary">
                ← Назад към списъка
            </a>
        </div>
    </div>
    <div class="card-body">

            <?php if (isset($error)): ?>
                <div class="alert alert-danger" role="alert">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if (isset($errors) && !empty($errors)): ?>
                <div class="alert alert-danger" role="alert">
                    <ul class="mb-0">
                        <?php foreach ($errors as $field => $error): ?>
                            <li><?= htmlspecialchars($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

        <div class="row">
            <div class="col-md-8">
                <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Данни за типа транзакция</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="/admin/strixbudget/transaction-types">
                                <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="name" class="form-label">Име *</label>
                                            <input type="text" 
                                                   class="form-control <?= isset($errors['name']) ? 'is-invalid' : '' ?>" 
                                                   id="name" 
                                                   name="name" 
                                                   value="<?= htmlspecialchars($old_input['name'] ?? '') ?>"
                                                   placeholder="Например: Храна, Транспорт, Заплата"
                                                   required>
                                            <div class="form-text">Въведете име на типа транзакция</div>
                                            <?php if (isset($errors['name'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['name']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="type" class="form-label">Тип *</label>
                                            <select class="form-select <?= isset($errors['type']) ? 'is-invalid' : '' ?>" 
                                                    id="type" 
                                                    name="type" 
                                                    required>
                                                <option value="">Изберете тип</option>
                                                <option value="income" <?= ($old_input['type'] ?? '') === 'income' ? 'selected' : '' ?>>
                                                    💰 Приход
                                                </option>
                                                <option value="expense" <?= ($old_input['type'] ?? '') === 'expense' ? 'selected' : '' ?>>
                                                    💸 Разход
                                                </option>
                                                <option value="both" <?= ($old_input['type'] ?? '') === 'both' ? 'selected' : '' ?>>
                                                    🔄 И двете
                                                </option>
                                            </select>
                                            <div class="form-text">За какъв тип транзакции се използва</div>
                                            <?php if (isset($errors['type'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['type']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="description" class="form-label">Описание</label>
                                    <textarea class="form-control <?= isset($errors['description']) ? 'is-invalid' : '' ?>" 
                                              id="description" 
                                              name="description" 
                                              rows="3"
                                              placeholder="Кратко описание на типа транзакция..."><?= htmlspecialchars($old_input['description'] ?? '') ?></textarea>
                                    <div class="form-text">Опционално описание за по-добра организация</div>
                                    <?php if (isset($errors['description'])): ?>
                                        <div class="invalid-feedback">
                                            <?= htmlspecialchars($errors['description']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="color" class="form-label">Цвят</label>
                                            <div class="input-group">
                                                <input type="color" 
                                                       class="form-control form-control-color <?= isset($errors['color']) ? 'is-invalid' : '' ?>" 
                                                       id="color" 
                                                       name="color" 
                                                       value="<?= htmlspecialchars($old_input['color'] ?? '#007bff') ?>"
                                                       title="Изберете цвят">
                                                <input type="text" 
                                                       class="form-control" 
                                                       id="color-text" 
                                                       placeholder="#007bff"
                                                       value="<?= htmlspecialchars($old_input['color'] ?? '#007bff') ?>">
                                            </div>
                                            <div class="form-text">Цвят за визуализация в графики и отчети</div>
                                            <?php if (isset($errors['color'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['color']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="icon" class="form-label">Икона</label>
                                            <div class="input-group">
                                                <span class="input-group-text">
                                                    <i id="icon-preview" class="<?= htmlspecialchars($old_input['icon'] ?? 'fas fa-tag') ?>"></i>
                                                </span>
                                                <input type="text" 
                                                       class="form-control <?= isset($errors['icon']) ? 'is-invalid' : '' ?>" 
                                                       id="icon" 
                                                       name="icon" 
                                                       value="<?= htmlspecialchars($old_input['icon'] ?? '') ?>"
                                                       placeholder="fas fa-tag">
                                            </div>
                                            <div class="form-text">FontAwesome икона клас (например: fas fa-shopping-cart)</div>
                                            <?php if (isset($errors['icon'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['icon']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="parent_id" class="form-label">Родителска категория</label>
                                            <select class="form-select <?= isset($errors['parent_id']) ? 'is-invalid' : '' ?>" 
                                                    id="parent_id" 
                                                    name="parent_id">
                                                <option value="">Без родител (основна категория)</option>
                                                <?php if (isset($parentTypes)): ?>
                                                    <?php foreach ($parentTypes as $parentType): ?>
                                                        <option value="<?= $parentType->id ?>" 
                                                                <?= ($old_input['parent_id'] ?? '') == $parentType->id ? 'selected' : '' ?>>
                                                            <?= htmlspecialchars($parentType->name) ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </select>
                                            <div class="form-text">Изберете родителска категория за йерархична организация</div>
                                            <?php if (isset($errors['parent_id'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['parent_id']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="sort_order" class="form-label">Ред на сортиране</label>
                                            <input type="number" 
                                                   class="form-control <?= isset($errors['sort_order']) ? 'is-invalid' : '' ?>" 
                                                   id="sort_order" 
                                                   name="sort_order" 
                                                   value="<?= htmlspecialchars($old_input['sort_order'] ?? '0') ?>"
                                                   min="0"
                                                   placeholder="0">
                                            <div class="form-text">По-ниските числа се показват първи</div>
                                            <?php if (isset($errors['sort_order'])): ?>
                                                <div class="invalid-feedback">
                                                    <?= htmlspecialchars($errors['sort_order']) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="is_active" 
                                               name="is_active" 
                                               value="1"
                                               <?= ($old_input['is_active'] ?? true) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="is_active">
                                            Активен тип
                                        </label>
                                    </div>
                                    <div class="form-text">Активните типове се показват в списъците за избор</div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="/admin/strixbudget/transaction-types" class="btn btn-secondary me-md-2">
                                        Отказ
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        💾 Създаване на тип
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-info">Помощ</h6>
                        </div>
                        <div class="card-body">
                            <h6>Типове транзакции:</h6>
                            <ul class="small">
                                <li><strong>Приход</strong> - само за приходни транзакции</li>
                                <li><strong>Разход</strong> - само за разходни транзакции</li>
                                <li><strong>И двете</strong> - може да се използва за всички</li>
                            </ul>

                            <hr>

                            <h6>Примери за категории:</h6>
                            <div class="small">
                                <strong>Разходи:</strong>
                                <ul>
                                    <li>🍔 Храна и напитки</li>
                                    <li>🚗 Транспорт</li>
                                    <li>🏠 Домакинство</li>
                                    <li>💊 Здравеопазване</li>
                                    <li>🎬 Развлечения</li>
                                </ul>
                                
                                <strong>Приходи:</strong>
                                <ul>
                                    <li>💼 Заплата</li>
                                    <li>💰 Бонуси</li>
                                    <li>📈 Инвестиции</li>
                                    <li>🎁 Подаръци</li>
                                </ul>
                            </div>

                            <hr>

                            <h6>Съвети:</h6>
                            <ul class="small">
                                <li>Използвайте кратки, ясни имена</li>
                                <li>Изберете подходящи цветове</li>
                                <li>Добавете икони за по-добра визуализация</li>
                                <li>Организирайте в йерархии при нужда</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Color Preview -->
                    <div class="card shadow mt-3">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-success">Преглед</h6>
                        </div>
                        <div class="card-body text-center">
                            <div id="type-preview" class="p-3 rounded" style="background-color: #f8f9fa; border: 2px dashed #dee2e6;">
                                <i id="preview-icon" class="fas fa-tag fa-2x mb-2" style="color: #007bff;"></i>
                                <h6 id="preview-name">Име на типа</h6>
                                <small id="preview-type" class="badge bg-secondary">Тип</small>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const typeSelect = document.getElementById('type');
    const colorInput = document.getElementById('color');
    const colorTextInput = document.getElementById('color-text');
    const iconInput = document.getElementById('icon');
    const iconPreview = document.getElementById('icon-preview');
    
    // Preview elements
    const previewIcon = document.getElementById('preview-icon');
    const previewName = document.getElementById('preview-name');
    const previewType = document.getElementById('preview-type');

    function updatePreview() {
        const name = nameInput.value || 'Име на типа';
        const type = typeSelect.value;
        const color = colorInput.value;
        const icon = iconInput.value || 'fas fa-tag';

        previewName.textContent = name;
        previewIcon.className = icon + ' fa-2x mb-2';
        previewIcon.style.color = color;

        const typeLabels = {
            'income': 'Приход',
            'expense': 'Разход',
            'both': 'И двете'
        };
        
        const typeClasses = {
            'income': 'bg-success',
            'expense': 'bg-danger',
            'both': 'bg-info'
        };

        previewType.textContent = typeLabels[type] || 'Тип';
        previewType.className = 'badge ' + (typeClasses[type] || 'bg-secondary');
    }

    // Color picker sync
    colorInput.addEventListener('input', function() {
        colorTextInput.value = this.value;
        updatePreview();
    });

    colorTextInput.addEventListener('input', function() {
        if (/^#[0-9A-F]{6}$/i.test(this.value)) {
            colorInput.value = this.value;
            updatePreview();
        }
    });

    // Icon preview
    iconInput.addEventListener('input', function() {
        iconPreview.className = this.value || 'fas fa-tag';
        updatePreview();
    });

    // Other inputs
    nameInput.addEventListener('input', updatePreview);
    typeSelect.addEventListener('change', updatePreview);

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const name = nameInput.value.trim();
            const type = typeSelect.value;

            if (!name) {
                alert('Моля въведете име на типа');
                e.preventDefault();
                return;
            }

            if (!type) {
                alert('Моля изберете тип');
                e.preventDefault();
                return;
            }
        });
    }

    // Initial preview
    updatePreview();
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../../layouts/app.php';
?>
