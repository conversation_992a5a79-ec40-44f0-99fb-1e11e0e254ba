<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h3>📂 Типове транзакции</h3>
        <div style="display: flex; gap: 10px; align-items: center;">
            <a href="/admin/strixbudget/transaction-types/create" class="btn btn-primary">
                ➕ Нов тип
            </a>
            <a href="/admin/strixbudget" class="btn btn-outline-secondary">
                ← Назад към dashboard
            </a>
        </div>
    </div>
    <div class="card-body">

            <!-- Search and Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label">Търсене</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="Име, описание..." 
                                       value="<?= htmlspecialchars($filters['search'] ?? '') ?>">
                                <button class="btn btn-outline-secondary" type="submit">🔍</button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label for="type" class="form-label">Тип</label>
                            <select class="form-select" id="type" name="type">
                                <option value="">Всички типове</option>
                                <option value="income" <?= ($filters['type'] ?? '') === 'income' ? 'selected' : '' ?>>Приход</option>
                                <option value="expense" <?= ($filters['type'] ?? '') === 'expense' ? 'selected' : '' ?>>Разход</option>
                                <option value="both" <?= ($filters['type'] ?? '') === 'both' ? 'selected' : '' ?>>И двете</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Статус</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">Всички</option>
                                <option value="active" <?= ($filters['status'] ?? '') === 'active' ? 'selected' : '' ?>>Активни</option>
                                <option value="inactive" <?= ($filters['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Неактивни</option>
                            </select>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-white bg-primary">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Общо типове</h6>
                                    <h4><?= count($transactionTypes) ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-tags fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">За приходи</h6>
                                    <h4><?= count(array_filter($transactionTypes, fn($t) => in_array($t->type ?? '', ['income', 'both']))) ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-arrow-up fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-danger">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">За разходи</h6>
                                    <h4><?= count(array_filter($transactionTypes, fn($t) => in_array($t->type ?? '', ['expense', 'both']))) ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-arrow-down fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-secondary">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Активни</h6>
                                    <h4><?= count(array_filter($transactionTypes, fn($t) => ($t->is_active ?? true))) ?></h4>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transaction Types Table -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Списък с типове транзакции</h6>
                </div>
                <div class="card-body">
                    <?php if (empty($transactionTypes)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Няма намерени типове транзакции</h5>
                            <p class="text-muted">Създайте първия си тип транзакция или променете филтрите.</p>
                            <a href="/admin/strixbudget/transaction-types/create" class="btn btn-primary">
                                ➕ Създаване на тип
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Име</th>
                                        <th>Тип</th>
                                        <th>Описание</th>
                                        <th>Цвят</th>
                                        <th>Статус</th>
                                        <th class="text-center">Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($transactionTypes as $type): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($type->icon ?? null): ?>
                                                        <i class="<?= htmlspecialchars($type->icon) ?> me-2"></i>
                                                    <?php endif; ?>
                                                    <strong><?= htmlspecialchars($type->name ?? 'Без име') ?></strong>
                                                </div>
                                            </td>
                                            <td>
                                                <?php
                                                $typeConfig = [
                                                    'income' => ['Приход', 'success', 'arrow-up'],
                                                    'expense' => ['Разход', 'danger', 'arrow-down'],
                                                    'both' => ['И двете', 'info', 'exchange-alt']
                                                ];
                                                $config = $typeConfig[$type->type ?? 'both'] ?? $typeConfig['both'];
                                                ?>
                                                <span class="badge bg-<?= $config[1] ?>">
                                                    <i class="fas fa-<?= $config[2] ?>"></i> <?= $config[0] ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($type->description): ?>
                                                    <span class="text-truncate" style="max-width: 200px; display: inline-block;" 
                                                          title="<?= htmlspecialchars($type->description) ?>">
                                                        <?= htmlspecialchars($type->description) ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($type->color): ?>
                                                    <div class="d-flex align-items-center">
                                                        <div class="color-preview me-2" 
                                                             style="width: 20px; height: 20px; background-color: <?= htmlspecialchars($type->color) ?>; border-radius: 3px; border: 1px solid #ddd;"></div>
                                                        <code><?= htmlspecialchars($type->color) ?></code>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (($type->is_active ?? true)): ?>
                                                    <span class="badge bg-success">Активен</span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Неактивен</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <div class="btn-group btn-group-sm">
                                                    <a href="/admin/strixbudget/transaction-types/<?= $type->id ?>" 
                                                       class="btn btn-outline-info" title="Преглед">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="/admin/strixbudget/transaction-types/<?= $type->id ?>/edit" 
                                                       class="btn btn-outline-warning" title="Редактиране">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-outline-danger" 
                                                            title="Изтриване"
                                                            onclick="deleteTransactionType(<?= $type->id ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination would go here -->
                        <?php if (isset($pagination)): ?>
                            <nav aria-label="Pagination">
                                <ul class="pagination justify-content-center">
                                    <!-- Pagination links -->
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
    </div>
</div>

<script>
function deleteTransactionType(id) {
    if (confirm('Сигурни ли сте, че искате да изтриете този тип транзакция?')) {
        fetch(`/admin/strixbudget/transaction-types/${id}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Грешка при изтриване: ' + data.message);
            }
        })
        .catch(error => {
            alert('Грешка при заявката: ' + error.message);
        });
    }
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../../layouts/app.php';
?>
