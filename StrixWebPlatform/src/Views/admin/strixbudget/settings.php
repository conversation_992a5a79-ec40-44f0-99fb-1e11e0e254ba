<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
        <h3>⚙️ StrixBudget Настройки</h3>
        <div style="display: flex; gap: 10px; align-items: center;">
            <a href="/admin/strixbudget" class="btn btn-outline-secondary">
                ← Назад към Dashboard
            </a>
        </div>
    </div>
    <div class="card-body">

            <?php if (isset($error)): ?>
                <div class="alert alert-danger" role="alert">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if (isset($errors) && !empty($errors)): ?>
                <div class="alert alert-danger" role="alert">
                    <ul class="mb-0">
                        <?php foreach ($errors as $field => $error): ?>
                            <li><?= htmlspecialchars($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">API Настройки</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="/admin/strixbudget/settings/update">
                                <input type="hidden" name="_token" value="<?= $csrf_token ?>">

                                <div class="form-group mb-3">
                                    <label for="api_url" class="form-label">API URL</label>
                                    <input type="url"
                                           class="form-control <?= isset($errors['api_url']) ? 'is-invalid' : '' ?>"
                                           id="api_url"
                                           name="api_url"
                                           value="<?= htmlspecialchars($old_input['api_url'] ?? $settings->api_url ?? 'http://localhost:8000/api') ?>"
                                           placeholder="http://localhost:8000/api"
                                           required>
                                    <div class="form-text">URL адресът на StrixBudget API</div>
                                    <?php if (isset($errors['api_url'])): ?>
                                        <div class="invalid-feedback">
                                            <?= htmlspecialchars($errors['api_url']) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="form-group mb-3">
                                    <label class="form-label">Метод на аутентикация</label>
                                    <div class="form-check">
                                        <input class="form-check-input"
                                               type="radio"
                                               name="auth_method"
                                               id="auth_method_token"
                                               value="token"
                                               <?= ($old_input['auth_method'] ?? $settings->auth_method ?? 'token') === 'token' ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="auth_method_token">
                                            🔑 API Токен (препоръчително)
                                        </label>
                                        <div class="form-text">Използвайте съществуващ API токен</div>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input"
                                               type="radio"
                                               name="auth_method"
                                               id="auth_method_credentials"
                                               value="credentials"
                                               <?= ($old_input['auth_method'] ?? $settings->auth_method ?? 'token') === 'credentials' ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="auth_method_credentials">
                                            👤 Имейл и парола
                                        </label>
                                        <div class="form-text">Влезте с вашите StrixBudget данни (токенът ще се получи автоматично)</div>
                                    </div>
                                </div>

                                <!-- Credentials fields (shown when credentials method is selected) -->
                                <div id="credentials_fields" style="display: none;">
                                    <div class="form-group mb-3">
                                        <label for="api_email" class="form-label">StrixBudget Имейл</label>
                                        <input type="email"
                                               class="form-control <?= isset($errors['api_email']) ? 'is-invalid' : '' ?>"
                                               id="api_email"
                                               name="api_email"
                                               value="<?= htmlspecialchars($old_input['api_email'] ?? '') ?>"
                                               placeholder="<EMAIL>">
                                        <div class="form-text">
                                            Вашият имейл за влизане в StrixBudget
                                            <?php if ($settings && $settings->api_email): ?>
                                                <br><small class="text-muted">Текущ имейл: <?= $settings->getMaskedEmail() ?></small>
                                            <?php endif; ?>
                                        </div>
                                        <?php if (isset($errors['api_email'])): ?>
                                            <div class="invalid-feedback">
                                                <?= htmlspecialchars($errors['api_email']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="form-group mb-3">
                                        <label for="api_password" class="form-label">StrixBudget Парола</label>
                                        <div class="input-group">
                                            <input type="password"
                                                   class="form-control <?= isset($errors['api_password']) ? 'is-invalid' : '' ?>"
                                                   id="api_password"
                                                   name="api_password"
                                                   value="<?= htmlspecialchars($old_input['api_password'] ?? '') ?>"
                                                   placeholder="Въведете парола">
                                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                                👁️
                                            </button>
                                        </div>
                                        <div class="form-text">
                                            Вашата парола за влизане в StrixBudget
                                            <?php if ($settings && $settings->api_password): ?>
                                                <br><small class="text-muted">Паролата е запазена: <?= $settings->getMaskedPassword() ?></small>
                                            <?php endif; ?>
                                        </div>
                                        <?php if (isset($errors['api_password'])): ?>
                                            <div class="invalid-feedback">
                                                <?= htmlspecialchars($errors['api_password']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Token field (shown when token method is selected) -->
                                <div id="token_fields">
                                    <div class="form-group mb-3">
                                        <label for="api_token" class="form-label">API Токен</label>
                                        <div class="input-group">
                                            <input type="password"
                                                   class="form-control <?= isset($errors['api_token']) ? 'is-invalid' : '' ?>"
                                                   id="api_token"
                                                   name="api_token"
                                                   value="<?= htmlspecialchars($old_input['api_token'] ?? '') ?>"
                                                   placeholder="Въведете API токен">
                                            <button class="btn btn-outline-secondary" type="button" id="toggleToken">
                                                👁️
                                            </button>
                                        </div>
                                        <div class="form-text">
                                            Bearer токен за аутентикация към StrixBudget API
                                            <?php if ($settings && $settings->api_token): ?>
                                                <br><small class="text-muted">Текущ токен: <?= $settings->getMaskedToken() ?></small>
                                            <?php endif; ?>
                                        </div>
                                        <?php if (isset($errors['api_token'])): ?>
                                            <div class="invalid-feedback">
                                                <?= htmlspecialchars($errors['api_token']) ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="is_active" 
                                               name="is_active" 
                                               value="1"
                                               <?= ($old_input['is_active'] ?? $settings->is_active ?? false) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="is_active">
                                            Активиране на StrixBudget интеграцията
                                        </label>
                                    </div>
                                    <div class="form-text">Когато е активирана, ще можете да използвате всички функции на StrixBudget</div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="button" class="btn btn-secondary me-md-2" id="debugInfo">
                                        🔧 Debug информация
                                    </button>
                                    <button type="button" class="btn btn-info me-md-2" id="testConnection">
                                        🔍 Тест на връзката
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        💾 Запазване
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-info">Информация</h6>
                        </div>
                        <div class="card-body">
                            <h6>Методи на аутентикация</h6>

                            <div class="mb-3">
                                <strong>🔑 API Токен (препоръчително)</strong>
                                <ol class="small">
                                    <li>Влезте в StrixBudget приложението</li>
                                    <li>Отидете в профила си</li>
                                    <li>Генерирайте нов API токен</li>
                                    <li>Копирайте токена и го поставете тук</li>
                                </ol>
                            </div>

                            <div class="mb-3">
                                <strong>👤 Имейл и парола</strong>
                                <ul class="small">
                                    <li>Използвайте вашите StrixBudget данни</li>
                                    <li>Токенът ще се получи автоматично</li>
                                    <li>По-лесно за настройка</li>
                                    <li>Данните се съхраняват криптирани</li>
                                </ul>
                            </div>

                            <hr>

                            <h6>Статус на връзката</h6>
                            <div id="connectionStatus">
                                <?php if ($settings && $settings->isConfigured()): ?>
                                    <span class="badge bg-success">Конфигурирано</span>
                                <?php else: ?>
                                    <span class="badge bg-warning">Не е конфигурирано</span>
                                <?php endif; ?>
                            </div>

                            <hr>

                            <h6>Действия</h6>
                            <?php if ($settings && $settings->api_token): ?>
                                <button type="button" class="btn btn-danger btn-sm" id="deleteSettings">
                                    🗑️ Изтриване на настройки
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if ($settings && $settings->isConfigured()): ?>
                    <div class="card shadow mt-3">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-success">Текущи настройки</h6>
                        </div>
                        <div class="card-body">
                            <p><strong>API URL:</strong><br>
                            <small class="text-muted"><?= htmlspecialchars($settings->api_url ?? '') ?></small></p>

                            <p><strong>Метод на аутентикация:</strong><br>
                            <small class="text-muted">
                                <?php if ($settings->getAuthMethod() === 'credentials'): ?>
                                    👤 Имейл и парола
                                <?php else: ?>
                                    🔑 API Токен
                                <?php endif; ?>
                            </small></p>

                            <?php if ($settings->usesCredentials()): ?>
                                <p><strong>Имейл:</strong><br>
                                <small class="text-muted"><?= $settings->getMaskedEmail() ?></small></p>

                                <p><strong>Парола:</strong><br>
                                <small class="text-muted"><?= $settings->getMaskedPassword() ?></small></p>
                            <?php endif; ?>

                            <?php if ($settings->usesToken() || ($settings->usesCredentials() && $settings->api_token)): ?>
                                <p><strong>API Токен:</strong><br>
                                <small class="text-muted"><?= $settings->getMaskedToken() ?></small></p>
                            <?php endif; ?>

                            <p><strong>Статус:</strong><br>
                            <?php if ($settings->isActive()): ?>
                                <span class="badge bg-success">Активен</span>
                            <?php else: ?>
                                <span class="badge bg-secondary">Неактивен</span>
                            <?php endif; ?>
                            </p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility for token
    const toggleToken = document.getElementById('toggleToken');
    const apiToken = document.getElementById('api_token');

    if (toggleToken && apiToken) {
        toggleToken.addEventListener('click', function() {
            if (apiToken.type === 'password') {
                apiToken.type = 'text';
                toggleToken.textContent = '🙈';
            } else {
                apiToken.type = 'password';
                toggleToken.textContent = '👁️';
            }
        });
    }

    // Toggle password visibility for password
    const togglePassword = document.getElementById('togglePassword');
    const apiPassword = document.getElementById('api_password');

    if (togglePassword && apiPassword) {
        togglePassword.addEventListener('click', function() {
            if (apiPassword.type === 'password') {
                apiPassword.type = 'text';
                togglePassword.textContent = '🙈';
            } else {
                apiPassword.type = 'password';
                togglePassword.textContent = '👁️';
            }
        });
    }

    // Handle auth method change
    const authMethodRadios = document.querySelectorAll('input[name="auth_method"]');
    const credentialsFields = document.getElementById('credentials_fields');
    const tokenFields = document.getElementById('token_fields');

    function toggleAuthFields() {
        const selectedMethod = document.querySelector('input[name="auth_method"]:checked').value;

        if (selectedMethod === 'credentials') {
            credentialsFields.style.display = 'block';
            tokenFields.style.display = 'none';

            // Make credentials required
            document.getElementById('api_email').required = true;
            document.getElementById('api_password').required = true;
            document.getElementById('api_token').required = false;
        } else {
            credentialsFields.style.display = 'none';
            tokenFields.style.display = 'block';

            // Make token required
            document.getElementById('api_email').required = false;
            document.getElementById('api_password').required = false;
            document.getElementById('api_token').required = true;
        }
    }

    // Set initial state
    toggleAuthFields();

    // Listen for changes
    authMethodRadios.forEach(radio => {
        radio.addEventListener('change', toggleAuthFields);
    });

    // Test connection
    const testConnection = document.getElementById('testConnection');
    if (testConnection) {
        testConnection.addEventListener('click', function() {
            const button = this;
            const originalText = button.textContent;
            
            button.disabled = true;
            button.textContent = '⏳ Тестване...';
            
            fetch('/admin/strixbudget/settings/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    _token: '<?= $csrf_token ?>'
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data && data.success) {
                    alert('✅ ' + (data.message || 'Връзката е успешна'));
                    document.getElementById('connectionStatus').innerHTML = '<span class="badge bg-success">Връзката работи</span>';
                } else {
                    alert('❌ ' + (data.message || 'Неизвестна грешка'));
                    document.getElementById('connectionStatus').innerHTML = '<span class="badge bg-danger">Грешка във връзката</span>';
                }
            })
            .catch(error => {
                console.error('Test connection error:', error);
                alert('❌ Грешка при тестване на връзката: ' + error.message);
                document.getElementById('connectionStatus').innerHTML = '<span class="badge bg-danger">Грешка във връзката</span>';
            })
            .finally(() => {
                button.disabled = false;
                button.textContent = originalText;
            });
        });
    }

    // Delete settings
    const deleteSettings = document.getElementById('deleteSettings');
    if (deleteSettings) {
        deleteSettings.addEventListener('click', function() {
            if (confirm('Сигурни ли сте, че искате да изтриете настройките? Това действие не може да бъде отменено.')) {
                fetch('/admin/strixbudget/settings/delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        _token: '<?= $csrf_token ?>'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ ' + data.message);
                        location.reload();
                    } else {
                        alert('❌ ' + data.message);
                    }
                })
                .catch(error => {
                    alert('❌ Грешка при изтриване: ' + error.message);
                });
            }
        });
    }

    // Debug info
    const debugInfo = document.getElementById('debugInfo');
    if (debugInfo) {
        debugInfo.addEventListener('click', function() {
            const button = this;
            const originalText = button.textContent;

            button.disabled = true;
            button.textContent = '⏳ Зареждане...';

            fetch('/admin/strixbudget/settings/debug', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                button.disabled = false;
                button.textContent = originalText;

                if (data.success) {
                    // Create a modal to show debug info
                    const debugData = JSON.stringify(data.data, null, 2);
                    const modal = document.createElement('div');
                    modal.className = 'modal fade';
                    modal.innerHTML = `
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">🔧 Debug информация</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto;">${debugData}</pre>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Затвори</button>
                                </div>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(modal);
                    const bsModal = new bootstrap.Modal(modal);
                    bsModal.show();

                    // Remove modal from DOM when hidden
                    modal.addEventListener('hidden.bs.modal', function() {
                        document.body.removeChild(modal);
                    });
                } else {
                    alert('❌ Грешка при зареждане на debug информацията: ' + (data.message || 'Неизвестна грешка'));
                }
            })
            .catch(error => {
                button.disabled = false;
                button.textContent = originalText;
                alert('❌ Грешка при заявката: ' + error.message);
            });
        });
    }
});
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
