<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">☁️ Nextcloud настройки</h4>
                </div>
                <div class="card-body">
                    <form id="nextcloudForm">
                        <input type="hidden" name="_token" value="<?= $csrf_token ?>">
                        
                        <!-- Server Configuration -->
                        <h5 class="mb-3">🌐 Сървър конфигурация</h5>
                        
                        <div class="form-group">
                            <label for="server_url">Nextcloud Server URL *</label>
                            <input type="url" id="server_url" name="server_url" class="form-control" 
                                   value="<?= htmlspecialchars($settings->server_url ?? '') ?>" 
                                   placeholder="https://cloud.example.com" required>
                            <small class="form-text text-muted">
                                Пълният URL адрес на вашия Nextcloud сървър
                            </small>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="username">Потребителско име *</label>
                                    <input type="text" id="username" name="username" class="form-control" 
                                           value="<?= htmlspecialchars($settings->username ?? '') ?>" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="password">App Password <?= $settings ? '' : '*' ?></label>
                                    <input type="password" id="password" name="password" class="form-control" 
                                           placeholder="<?= $settings ? 'Оставете празно за да запазите текущата' : 'Въведете App Password' ?>"
                                           <?= $settings ? '' : 'required' ?>>
                                    <small class="form-text text-muted">
                                        Създайте App Password в Nextcloud Settings → Security
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Connection Settings -->
                        <h5 class="mb-3 mt-4">🔧 Настройки на връзката</h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="timeout">Timeout (секунди)</label>
                                    <input type="number" id="timeout" name="timeout" class="form-control" 
                                           value="<?= $settings->timeout ?? 30 ?>" min="5" max="300">
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="default_folder">Папка по подразбиране</label>
                                    <input type="text" id="default_folder" name="default_folder" class="form-control" 
                                           value="<?= htmlspecialchars($settings->default_folder ?? '/ERP_Files') ?>" 
                                           placeholder="/ERP_Files">
                                    <small class="form-text text-muted">
                                        Папка за качване на файлове от ERP системата
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Advanced Settings -->
                        <h5 class="mb-3 mt-4">⚙️ Разширени настройки</h5>
                        
                        <div class="form-check mb-3">
                            <input type="checkbox" id="verify_ssl" name="verify_ssl" class="form-check-input" 
                                   <?= ($settings->verify_ssl ?? true) ? 'checked' : '' ?>>
                            <label for="verify_ssl" class="form-check-label">
                                Проверка на SSL сертификат
                            </label>
                            <small class="form-text text-muted">
                                Препоръчва се да остане включено за сигурност
                            </small>
                        </div>
                        
                        <div class="form-check mb-4">
                            <input type="checkbox" id="auto_create_folders" name="auto_create_folders" class="form-check-input" 
                                   <?= ($settings->auto_create_folders ?? true) ? 'checked' : '' ?>>
                            <label for="auto_create_folders" class="form-check-label">
                                Автоматично създаване на папки
                            </label>
                            <small class="form-text text-muted">
                                Създавай папки автоматично ако не съществуват
                            </small>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="form-group">
                            <button type="submit" class="btn btn-success">
                                💾 Запази настройките
                            </button>
                            
                            <button type="button" class="btn btn-info ml-2" onclick="testConnection()">
                                🔄 Тествай връзката
                            </button>
                            
                            <?php if ($settings): ?>
                                <button type="button" class="btn btn-danger ml-2" onclick="deleteSettings()">
                                    🗑️ Изтрий настройките
                                </button>
                            <?php endif; ?>
                            
                            <a href="/admin/profile" class="btn btn-secondary ml-2">
                                ↩️ Обратно към профила
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Current Status -->
            <?php if ($settings): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">📊 Текущ статус</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Статус:</strong></td>
                                <td>
                                    <?php if ($settings->last_test_result === 'success'): ?>
                                        <span class="badge badge-success">✅ Свързан</span>
                                    <?php elseif ($settings->last_test_result === 'failed'): ?>
                                        <span class="badge badge-danger">❌ Грешка</span>
                                    <?php else: ?>
                                        <span class="badge badge-warning">⏳ Не е тестван</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            
                            <?php if ($settings->last_tested_at): ?>
                                <tr>
                                    <td><strong>Последен тест:</strong></td>
                                    <td><?= date('d.m.Y H:i', strtotime($settings->last_tested_at)) ?></td>
                                </tr>
                            <?php endif; ?>
                            
                            <?php if ($settings->last_error_message): ?>
                                <tr>
                                    <td><strong>Последна грешка:</strong></td>
                                    <td>
                                        <small class="text-danger">
                                            <?= htmlspecialchars($settings->last_error_message) ?>
                                        </small>
                                    </td>
                                </tr>
                            <?php endif; ?>
                            
                            <tr>
                                <td><strong>Създадено:</strong></td>
                                <td><?= date('d.m.Y H:i', strtotime($settings->created_at)) ?></td>
                            </tr>
                            
                            <tr>
                                <td><strong>Обновено:</strong></td>
                                <td><?= date('d.m.Y H:i', strtotime($settings->updated_at)) ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Help Card -->
            <div class="card <?= $settings ? 'mt-3' : '' ?>">
                <div class="card-header">
                    <h5 class="mb-0">💡 Помощ</h5>
                </div>
                <div class="card-body">
                    <h6>Как да създам App Password:</h6>
                    <ol class="small">
                        <li>Влезте в Nextcloud</li>
                        <li>Отидете в Settings → Security</li>
                        <li>В секция "App passwords" въведете име (напр. "ERP System")</li>
                        <li>Натиснете "Create new app password"</li>
                        <li>Копирайте генерираната парола</li>
                        <li>Поставете я в полето "App Password" по-горе</li>
                    </ol>
                    
                    <h6 class="mt-3">Защо App Password:</h6>
                    <p class="small text-muted">
                        App Password е по-сигурен начин за достъп до Nextcloud от външни приложения. 
                        Можете да го отмените по всяко време без да променяте основната си парола.
                    </p>
                    
                    <h6 class="mt-3">Проблеми с връзката:</h6>
                    <ul class="small">
                        <li>Проверете дали URL адресът е правилен</li>
                        <li>Уверете се, че App Password е валидна</li>
                        <li>Проверете SSL настройките</li>
                        <li>Свържете се с администратора на Nextcloud</li>
                    </ul>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">⚡ Бързи действия</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if ($settings && $settings->last_test_result === 'success'): ?>
                            <a href="/admin/storage" class="btn btn-outline-primary btn-sm">
                                💾 Отвори файловия мениджър
                            </a>
                        <?php endif; ?>
                        
                        <a href="/admin/profile" class="btn btn-outline-secondary btn-sm">
                            👤 Обратно към профила
                        </a>
                        
                        <a href="/admin" class="btn btn-outline-secondary btn-sm">
                            🏠 Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<div id="messageContainer"></div>

<script>
// Form submission
document.getElementById('nextcloudForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('/admin/profile/nextcloud-settings/update', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showMessage('error', data.error);
        }
    })
    .catch(error => {
        showMessage('error', 'Грешка при запазване: ' + error.message);
    });
});

// Test connection
function testConnection() {
    const form = document.getElementById('nextcloudForm');
    const formData = new FormData(form);

    showMessage('info', 'Тестване на връзката...');

    fetch('/admin/profile/nextcloud-settings/test', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('success', data.message);
            setTimeout(() => location.reload(), 1500);
        } else {
            showMessage('error', data.error);
        }
    })
    .catch(error => {
        showMessage('error', 'Грешка при тестване: ' + error.message);
    });
}

// Delete settings
function deleteSettings() {
    if (!confirm('Сигурни ли сте, че искате да изтриете Nextcloud настройките?')) {
        return;
    }

    const formData = new FormData();
    const tokenInput = document.querySelector('input[name="_token"]');
    if (tokenInput) {
        formData.append('_token', tokenInput.value);
    }

    fetch('/admin/profile/nextcloud-settings/delete', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('success', data.message);
            setTimeout(() => window.location.href = '/admin/profile', 1500);
        } else {
            showMessage('error', data.error);
        }
    })
    .catch(error => {
        showMessage('error', 'Грешка при изтриване: ' + error.message);
    });
}

// Show message function
function showMessage(type, message) {
    const container = document.getElementById('messageContainer');
    let alertClass, icon;
    
    switch(type) {
        case 'success':
            alertClass = 'alert-success';
            icon = '✅';
            break;
        case 'error':
            alertClass = 'alert-danger';
            icon = '❌';
            break;
        case 'info':
            alertClass = 'alert-info';
            icon = 'ℹ️';
            break;
        default:
            alertClass = 'alert-secondary';
            icon = '';
    }
    
    container.innerHTML = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${icon} ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    // Auto-hide after 5 seconds (except for info messages)
    if (type !== 'info') {
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
