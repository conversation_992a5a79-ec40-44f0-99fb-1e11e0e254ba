<?php
ob_start();
?>

<div class="card">
    <div class="card-header">
        <h3>Създаване на нов потребител</h3>
    </div>
    
    <div class="card-body">
        <?php if (isset($error)): ?>
            <div class="alert alert-error">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($errors) && !empty($errors)): ?>
            <div class="alert alert-error">
                <ul style="margin: 0; padding-left: 20px;">
                    <?php foreach ($errors as $field => $fieldErrors): ?>
                        <?php foreach ($fieldErrors as $error): ?>
                            <li><?= htmlspecialchars($error) ?></li>
                        <?php endforeach; ?>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="/admin/users" data-validate>
            <input type="hidden" name="_token" value="<?= $csrf_token ?>">
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <div class="form-group">
                        <label for="username">Потребителско име *</label>
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            class="form-control" 
                            value="<?= htmlspecialchars($old_input['username'] ?? '') ?>"
                            required
                            minlength="3"
                            maxlength="50"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Имейл адрес *</label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            class="form-control" 
                            value="<?= htmlspecialchars($old_input['email'] ?? '') ?>"
                            required
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="first_name">Име *</label>
                        <input 
                            type="text" 
                            id="first_name" 
                            name="first_name" 
                            class="form-control" 
                            value="<?= htmlspecialchars($old_input['first_name'] ?? '') ?>"
                            required
                            maxlength="50"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="last_name">Фамилия *</label>
                        <input 
                            type="text" 
                            id="last_name" 
                            name="last_name" 
                            class="form-control" 
                            value="<?= htmlspecialchars($old_input['last_name'] ?? '') ?>"
                            required
                            maxlength="50"
                        >
                    </div>
                </div>
                
                <div>
                    <div class="form-group">
                        <label for="password">Парола *</label>
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-control" 
                            required
                            minlength="8"
                            data-min-length="8"
                        >
                        <small style="color: #7f8c8d;">Минимум 8 символа</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="password_confirm">Потвърди парола *</label>
                        <input 
                            type="password" 
                            id="password_confirm" 
                            name="password_confirm" 
                            class="form-control" 
                            required
                            minlength="8"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <input 
                                type="checkbox" 
                                name="is_active" 
                                value="1"
                                <?= isset($old_input['is_active']) && $old_input['is_active'] ? 'checked' : 'checked' ?>
                                style="margin-right: 8px;"
                            >
                            Активен акаунт
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label>Групи</label>
                        <div style="max-height: 150px; overflow-y: auto; border: 1px solid #ced4da; padding: 10px; border-radius: 4px;">
                            <?php if (!empty($groups)): ?>
                                <?php foreach ($groups as $group): ?>
                                    <label style="display: block; margin-bottom: 5px; font-weight: normal;">
                                        <input 
                                            type="checkbox" 
                                            name="groups[]" 
                                            value="<?= $group->id ?>"
                                            <?= isset($old_input['groups']) && is_array($old_input['groups']) && in_array($group->id, $old_input['groups']) ? 'checked' : '' ?>
                                            style="margin-right: 8px;"
                                        >
                                        <?= htmlspecialchars($group->name) ?>
                                        <?php if ($group->description): ?>
                                            <small style="color: #7f8c8d;"> - <?= htmlspecialchars($group->description) ?></small>
                                        <?php endif; ?>
                                    </label>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p style="color: #7f8c8d; margin: 0;">Няма налични групи</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-group" style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                <button type="submit" class="btn btn-success">
                    ✅ Създай потребител
                </button>
                <a href="/admin/users" class="btn btn-warning" style="margin-left: 10px;">
                    ↩️ Отказ
                </a>
            </div>
        </form>
    </div>
</div>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
