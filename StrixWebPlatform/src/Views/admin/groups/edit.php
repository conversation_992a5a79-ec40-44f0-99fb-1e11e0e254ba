<?php
use Strix\ERP\Core\Application;

$app = Application::getInstance();
ob_start();
?>

<div class="card">
    <div class="card-header">
        <h3>Редактиране на група: <?= htmlspecialchars($group->name) ?></h3>
    </div>
    
    <div class="card-body">
        <?php if (isset($error)): ?>
            <div class="alert alert-error">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <?php if (isset($errors) && !empty($errors)): ?>
            <div class="alert alert-error">
                <ul style="margin: 0; padding-left: 20px;">
                    <?php foreach ($errors as $field => $fieldErrors): ?>
                        <?php foreach ($fieldErrors as $error): ?>
                            <li><?= htmlspecialchars($error) ?></li>
                        <?php endforeach; ?>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="/admin/groups/<?= $group->id ?>" data-validate>
            <input type="hidden" name="_method" value="PUT">
            <input type="hidden" name="_token" value="<?= $csrf_token ?>">
            
            <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 30px;">
                <div>
                    <div class="form-group">
                        <label for="name">Име на групата *</label>
                        <input 
                            type="text" 
                            id="name" 
                            name="name" 
                            class="form-control" 
                            value="<?= htmlspecialchars($old_input['name'] ?? $group->name) ?>"
                            required
                            minlength="3"
                            maxlength="50"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Описание</label>
                        <textarea 
                            id="description" 
                            name="description" 
                            class="form-control" 
                            rows="4"
                            maxlength="255"
                        ><?= htmlspecialchars($old_input['description'] ?? $group->description) ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <input 
                                type="checkbox" 
                                name="is_active" 
                                value="1"
                                <?= (isset($old_input['is_active']) ? $old_input['is_active'] : $group->is_active) ? 'checked' : '' ?>
                                style="margin-right: 8px;"
                            >
                            Активна група
                        </label>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; margin-top: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #2c3e50;">Информация за групата</h4>
                        <div>
                            <strong>Потребители:</strong> <?= $group->getUserCount() ?><br>
                            <strong>Създадена:</strong> <?= date('d.m.Y H:i', strtotime($group->created_at)) ?><br>
                            <strong>Последна промяна:</strong> <?= date('d.m.Y H:i', strtotime($group->updated_at)) ?>
                        </div>
                    </div>
                </div>
                
                <div>
                    <?php if ($app->hasPermission('groups.manage_permissions')): ?>
                    <div class="form-group">
                        <label>Права на групата</label>
                        <div style="max-height: 400px; overflow-y: auto; border: 1px solid #ced4da; padding: 15px; border-radius: 4px; background: #f8f9fa;">
                            <?php if (!empty($permissions)): ?>
                                <?php foreach ($permissions as $module => $modulePermissions): ?>
                                    <div style="margin-bottom: 20px;">
                                        <h4 style="color: #2c3e50; margin-bottom: 10px; padding-bottom: 5px; border-bottom: 2px solid #3498db;">
                                            📁 <?= ucfirst(htmlspecialchars($module)) ?>
                                        </h4>
                                        
                                        <div style="margin-left: 15px;">
                                            <?php foreach ($modulePermissions as $permission): ?>
                                                <?php 
                                                $isChecked = false;
                                                if (isset($old_input['permissions']) && is_array($old_input['permissions'])) {
                                                    $isChecked = in_array($permission->id, $old_input['permissions']);
                                                } else {
                                                    $isChecked = in_array($permission->id, $groupPermissionIds);
                                                }
                                                ?>
                                                <label style="display: block; margin-bottom: 8px; font-weight: normal; padding: 5px; border-radius: 3px; background: white;">
                                                    <input 
                                                        type="checkbox" 
                                                        name="permissions[]" 
                                                        value="<?= $permission->id ?>"
                                                        <?= $isChecked ? 'checked' : '' ?>
                                                        style="margin-right: 8px;"
                                                    >
                                                    <strong><?= htmlspecialchars($permission->name) ?></strong>
                                                    <?php if ($permission->description): ?>
                                                        <br>
                                                        <small style="color: #7f8c8d; margin-left: 20px;">
                                                            <?= htmlspecialchars($permission->description) ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </label>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p style="color: #7f8c8d; margin: 0; text-align: center; padding: 20px;">
                                    Няма налични права
                                </p>
                            <?php endif; ?>
                        </div>
                        
                        <div style="margin-top: 10px; display: flex; gap: 10px;">
                            <button type="button" onclick="selectAllPermissions()" class="btn btn-primary btn-sm">
                                Избери всички
                            </button>
                            <button type="button" onclick="deselectAllPermissions()" class="btn btn-warning btn-sm">
                                Премахни всички
                            </button>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="form-group">
                        <label>Права на групата</label>
                        <div style="border: 1px solid #ced4da; padding: 15px; border-radius: 4px; background: #f8f9fa;">
                            <p style="color: #7f8c8d; margin: 0; text-align: center;">
                                Нямате права за управление на правата на групи
                            </p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="form-group" style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                <button type="submit" class="btn btn-success">
                    ✅ Запази промените
                </button>
                <a href="/admin/groups" class="btn btn-warning" style="margin-left: 10px;">
                    ↩️ Отказ
                </a>
            </div>
        </form>
    </div>
</div>

<script>
function selectAllPermissions() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = true);
}

function deselectAllPermissions() {
    const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
    checkboxes.forEach(checkbox => checkbox.checked = false);
}
</script>

<?php
$content = ob_get_clean();
include __DIR__ . '/../../layouts/app.php';
?>
