<?php

namespace Strix\ERP\Middleware;

use Strix\ERP\Core\Application;

class AdminMiddleware
{
    public function handle(): void
    {
        $app = Application::getInstance();

        // Check if user is logged in
        if (!$app->isLoggedIn()) {
            $_SESSION['intended_url'] = $_SERVER['REQUEST_URI'];
            $app->redirect('/login');
        }

        // Check if user has admin permissions
        $user = $app->getCurrentUser();

        // Check if user is admin OR has any admin-level permissions
        $isAdmin = $user['is_admin'] ?? false;
        $hasAdminPermissions = $app->hasPermission('users.view') ||
                              $app->hasPermission('groups.view') ||
                              $app->hasPermission('tasks.view') ||
                              $app->hasPermission('storage.view') ||
                              $app->hasPermission('nextcloud.view_personal') ||
                              $app->hasPermission('nextcloud.manage_personal') ||
                              $app->hasPermission('admin.system_settings');

        if (!$isAdmin && !$hasAdminPermissions) {
            $app->setFlashMessage('error', 'Нямате права за достъп до тази страница');
            $app->redirect('/');
        }
    }
}
