<?php

namespace Strix\ERP\Models;

use Strix\ERP\Core\Model;
use Strix\ERP\Core\Database;

class Group extends Model
{
    protected string $table = 'groups';
    
    protected array $fillable = [
        'name', 'description', 'is_active'
    ];
    
    protected array $casts = [
        'is_active' => 'boolean'
    ];

    public static function findByName(string $name): ?self
    {
        return self::findBy('name', $name);
    }

    public function getUsers(): array
    {
        $results = Database::fetchAll(
            "SELECT u.* FROM users u 
             INNER JOIN user_groups ug ON u.id = ug.user_id 
             WHERE ug.group_id = ? AND u.is_active = 1",
            [$this->id]
        );
        
        $users = [];
        foreach ($results as $data) {
            $user = new User();
            foreach ($data as $key => $value) {
                $user->$key = $value;
            }
            $users[] = $user;
        }
        return $users;
    }

    public function getPermissions(): array
    {
        $results = Database::fetchAll(
            "SELECT p.* FROM permissions p
             INNER JOIN group_permissions gp ON p.id = gp.permission_id
             WHERE gp.group_id = ?",
            [$this->id]
        );
        
        $permissions = [];
        foreach ($results as $data) {
            $permission = new Permission();
            foreach ($data as $key => $value) {
                $permission->$key = $value;
            }
            $permissions[] = $permission;
        }
        return $permissions;
    }

    public function getPermissionNames(): array
    {
        $permissions = $this->getPermissions();
        return array_map(fn($permission) => $permission->name, $permissions);
    }

    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->getPermissionNames());
    }

    public function addPermission(int $permissionId, ?int $grantedBy = null): bool
    {
        try {
            Database::insert('group_permissions', [
                'group_id' => $this->id,
                'permission_id' => $permissionId,
                'granted_by' => $grantedBy
            ]);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function removePermission(int $permissionId): bool
    {
        return Database::delete('group_permissions', [
            'group_id' => $this->id,
            'permission_id' => $permissionId
        ]) > 0;
    }

    public function syncPermissions(array $permissionIds, ?int $grantedBy = null): void
    {
        // Remove all existing permissions
        Database::delete('group_permissions', ['group_id' => $this->id]);
        
        // Add new permissions
        foreach ($permissionIds as $permissionId) {
            $this->addPermission($permissionId, $grantedBy);
        }
    }

    public function getUserCount(): int
    {
        return (int) Database::fetchColumn(
            "SELECT COUNT(*) FROM user_groups WHERE group_id = ?",
            [$this->id]
        );
    }

    public function isActive(): bool
    {
        return (bool) $this->is_active;
    }

    public static function getActiveGroups(): array
    {
        return self::where('is_active', 1);
    }

    public static function searchGroups(string $query): array
    {
        $results = Database::fetchAll(
            "SELECT * FROM groups 
             WHERE (name LIKE ? OR description LIKE ?)
             ORDER BY name",
            ["%$query%", "%$query%"]
        );
        
        $groups = [];
        foreach ($results as $data) {
            $group = new self();
            foreach ($data as $key => $value) {
                $group->$key = $value;
            }
            $groups[] = $group;
        }
        return $groups;
    }
}
