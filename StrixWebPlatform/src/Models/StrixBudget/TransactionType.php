<?php

namespace Strix\ERP\Models\StrixBudget;

class TransactionType extends BaseStrixBudgetModel
{
    protected array $fillable = [
        'id',
        'name',
        'description',
        'created_at',
        'updated_at',
        'user_id',
        'deleted_at',
        'transactions_count'
    ];
    
    protected function getResourceName(): string
    {
        return 'transactionType';
    }
    
    /**
     * Get transaction type statistics
     */
    public function getStatistics(): array
    {
        try {
            if (!isset($this->attributes['id'])) {
                return [];
            }
            
            $response = self::getClient()->getTransactionTypeStatistics($this->attributes['id']);
            
            if ($response['success'] && isset($response['data'])) {
                return $response['data'];
            }
            
            return [];
        } catch (\Exception $e) {
            error_log("Error getting transaction type statistics: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get transaction type transactions
     */
    public function getTransactions(): array
    {
        try {
            if (!isset($this->attributes['id'])) {
                return [];
            }
            
            $response = self::getClient()->getTransactionTypeTransactions($this->attributes['id']);
            
            if ($response['success'] && isset($response['data'])) {
                $transactions = [];
                foreach ($response['data'] as $item) {
                    $transactions[] = new Transaction($item);
                }
                return $transactions;
            }
            
            return [];
        } catch (\Exception $e) {
            error_log("Error getting transaction type transactions: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get display name
     */
    public function getDisplayName(): string
    {
        return $this->attributes['name'] ?? 'Unknown Type';
    }
    
    /**
     * Get description or fallback
     */
    public function getDescription(): string
    {
        return $this->attributes['description'] ?? 'Няма описание';
    }
    
    /**
     * Check if has description
     */
    public function hasDescription(): bool
    {
        return !empty($this->attributes['description']);
    }
    
    /**
     * Get transaction type icon based on name
     */
    public function getIcon(): string
    {
        $name = strtolower($this->getDisplayName());
        
        // Common transaction type icons
        $iconMap = [
            'salary' => '💰',
            'заплата' => '💰',
            'food' => '🍽️',
            'храна' => '🍽️',
            'transport' => '🚗',
            'транспорт' => '🚗',
            'entertainment' => '🎬',
            'развлечения' => '🎬',
            'shopping' => '🛒',
            'пазаруване' => '🛒',
            'utilities' => '🏠',
            'комунални' => '🏠',
            'healthcare' => '🏥',
            'здравеопазване' => '🏥',
            'education' => '📚',
            'образование' => '📚',
            'investment' => '📈',
            'инвестиция' => '📈',
            'loan' => '🏦',
            'заем' => '🏦',
            'gift' => '🎁',
            'подарък' => '🎁',
            'bonus' => '🎉',
            'бонус' => '🎉'
        ];
        
        foreach ($iconMap as $keyword => $icon) {
            if (strpos($name, $keyword) !== false) {
                return $icon;
            }
        }
        
        return '💼'; // Default icon
    }
    
    /**
     * Get color based on transaction type name
     */
    public function getColor(): string
    {
        $name = strtolower($this->getDisplayName());
        
        // Income-related types
        $incomeKeywords = ['salary', 'заплата', 'bonus', 'бонус', 'investment', 'инвестиция'];
        foreach ($incomeKeywords as $keyword) {
            if (strpos($name, $keyword) !== false) {
                return '#28a745'; // Green
            }
        }
        
        // Expense-related types
        $expenseKeywords = ['food', 'храна', 'transport', 'транспорт', 'shopping', 'пазаруване'];
        foreach ($expenseKeywords as $keyword) {
            if (strpos($name, $keyword) !== false) {
                return '#dc3545'; // Red
            }
        }
        
        // Neutral types
        return '#6c757d'; // Gray
    }
    
    /**
     * Get transaction types for select options
     */
    public static function getSelectOptions(): array
    {
        $types = self::all();
        $options = [];
        
        foreach ($types as $type) {
            $options[] = [
                'value' => $type->attributes['id'],
                'text' => $type->getDisplayName(),
                'icon' => $type->getIcon()
            ];
        }
        
        return $options;
    }
    
    /**
     * Find transaction type by name
     */
    public static function findByName(string $name): ?self
    {
        $types = self::all();
        
        foreach ($types as $type) {
            if (strcasecmp($type->getDisplayName(), $name) === 0) {
                return $type;
            }
        }
        
        return null;
    }
}
