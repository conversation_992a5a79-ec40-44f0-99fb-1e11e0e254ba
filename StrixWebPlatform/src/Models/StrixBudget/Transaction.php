<?php

namespace Strix\ERP\Models\StrixBudget;

class Transaction extends BaseStrixBudgetModel
{
    protected array $fillable = [
        'id',
        'user_id',
        'bank_account_id',
        'counterparty_id',
        'transaction_type_id',
        'type',
        'amount',
        'currency',
        'description',
        'attachment_path',
        'executed_at',
        'created_at',
        'updated_at',
        'deleted_at'
    ];
    
    protected function getResourceName(): string
    {
        return 'transaction';
    }
    
    /**
     * Get transactions with filters
     */
    public static function getFiltered(array $filters = []): array
    {
        try {
            $response = self::getClient()->getTransactions($filters);

            if ($response['success'] && isset($response['data'])) {
                $transactions = [];

                // Handle different response structures
                $transactionData = [];

                if (isset($response['data']['data'])) {
                    // Real API response with pagination
                    $transactionData = $response['data']['data'];
                } elseif (is_array($response['data']) && !isset($response['data']['current_page'])) {
                    // Mock API response (simple array)
                    $transactionData = $response['data'];
                } else {
                    error_log("Unexpected transaction data structure: " . print_r($response['data'], true));
                    return [];
                }

                foreach ($transactionData as $item) {
                    // Ensure $item is an array
                    if (is_array($item)) {
                        $transactions[] = new static($item);
                    } else {
                        error_log("Invalid transaction data format: " . print_r($item, true));
                    }
                }
                return $transactions;
            }

            return [];
        } catch (\Exception $e) {
            error_log("Error getting filtered transactions: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return [];
        }
    }
    
    /**
     * Get formatted amount
     */
    public function getFormattedAmount(): string
    {
        $amount = $this->attributes['amount'] ?? 0;
        $currency = $this->attributes['currency'] ?? 'EUR';
        $type = $this->attributes['type'] ?? 'expense';
        
        $sign = $type === 'income' ? '+' : '-';
        return $sign . number_format($amount, 2) . ' ' . $currency;
    }
    
    /**
     * Get amount with sign
     */
    public function getSignedAmount(): float
    {
        $amount = (float) ($this->attributes['amount'] ?? 0);
        $type = $this->attributes['type'] ?? 'expense';
        
        return $type === 'income' ? $amount : -$amount;
    }
    
    /**
     * Check if transaction is income
     */
    public function isIncome(): bool
    {
        return ($this->attributes['type'] ?? '') === 'income';
    }
    
    /**
     * Check if transaction is expense
     */
    public function isExpense(): bool
    {
        return ($this->attributes['type'] ?? '') === 'expense';
    }
    
    /**
     * Get formatted date
     */
    public function getFormattedDate(): string
    {
        $date = $this->attributes['executed_at'] ?? '';
        if ($date) {
            return date('d.m.Y', strtotime($date));
        }
        return '';
    }
    
    /**
     * Get formatted datetime
     */
    public function getFormattedDateTime(): string
    {
        $date = $this->attributes['executed_at'] ?? '';
        if ($date) {
            return date('d.m.Y H:i', strtotime($date));
        }
        return '';
    }
    
    /**
     * Get transaction type color class
     */
    public function getTypeColorClass(): string
    {
        return $this->isIncome() ? 'text-success' : 'text-danger';
    }
    
    /**
     * Get transaction type icon
     */
    public function getTypeIcon(): string
    {
        return $this->isIncome() ? '📈' : '📉';
    }
    
    /**
     * Get transactions by bank account
     */
    public static function getByBankAccount(int $bankAccountId, array $additionalFilters = []): array
    {
        $filters = array_merge(['bank_account_id' => $bankAccountId], $additionalFilters);
        return self::getFiltered($filters);
    }
    
    /**
     * Get transactions by counterparty
     */
    public static function getByCounterparty(int $counterpartyId, array $additionalFilters = []): array
    {
        $filters = array_merge(['counterparty_id' => $counterpartyId], $additionalFilters);
        return self::getFiltered($filters);
    }
    
    /**
     * Get transactions by type
     */
    public static function getByType(string $type, array $additionalFilters = []): array
    {
        $filters = array_merge(['type' => $type], $additionalFilters);
        return self::getFiltered($filters);
    }
    
    /**
     * Get transactions by transaction type
     */
    public static function getByTransactionType(int $transactionTypeId, array $additionalFilters = []): array
    {
        $filters = array_merge(['transaction_type_id' => $transactionTypeId], $additionalFilters);
        return self::getFiltered($filters);
    }
    
    /**
     * Get transactions by date range
     */
    public static function getByDateRange(string $fromDate, string $toDate, array $additionalFilters = []): array
    {
        $filters = array_merge([
            'from_date' => $fromDate,
            'to_date' => $toDate
        ], $additionalFilters);
        return self::getFiltered($filters);
    }
    
    /**
     * Get income transactions
     */
    public static function getIncome(array $additionalFilters = []): array
    {
        return self::getByType('income', $additionalFilters);
    }
    
    /**
     * Get expense transactions
     */
    public static function getExpenses(array $additionalFilters = []): array
    {
        return self::getByType('expense', $additionalFilters);
    }
}
