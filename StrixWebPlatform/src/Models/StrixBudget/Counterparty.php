<?php

namespace Strix\ERP\Models\StrixBudget;

class Counterparty extends BaseStrixBudgetModel
{
    protected array $fillable = [
        'id',
        'user_id',
        'name',
        'email',
        'phone',
        'description',
        'is_active',
        'created_at',
        'updated_at',
        'deleted_at',
        'transactions_count',
        'total_income',
        'total_expenses'
    ];
    
    protected function getResourceName(): string
    {
        return 'counterparty';
    }
    
    /**
     * Get counterparties with search
     */
    public static function search(string $query, int $perPage = 20): array
    {
        try {
            $filters = [
                'search' => $query,
                'per_page' => $perPage
            ];

            $response = self::getClient()->getCounterparties($filters);

            if ($response['success'] && isset($response['data'])) {
                $counterparties = [];

                // Handle pagination structure
                $counterpartyData = [];
                if (isset($response['data']['data'])) {
                    // Paginated API response
                    $counterpartyData = $response['data']['data'];
                } elseif (is_array($response['data']) && !isset($response['data']['current_page'])) {
                    // Simple array response
                    $counterpartyData = $response['data'];
                }

                foreach ($counterpartyData as $item) {
                    if (is_array($item)) {
                        $counterparties[] = new static($item);
                    }
                }
                return $counterparties;
            }

            return [];
        } catch (\Exception $e) {
            error_log("Error searching counterparties: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get counterparty statistics
     */
    public function getStatistics(): array
    {
        try {
            if (!isset($this->attributes['id'])) {
                return [];
            }
            
            $response = self::getClient()->getCounterpartyStatistics($this->attributes['id']);
            
            if ($response['success'] && isset($response['data'])) {
                return $response['data'];
            }
            
            return [];
        } catch (\Exception $e) {
            error_log("Error getting counterparty statistics: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get counterparty transactions
     */
    public function getTransactions(): array
    {
        try {
            if (!isset($this->attributes['id'])) {
                return [];
            }
            
            $response = self::getClient()->getCounterpartyTransactions($this->attributes['id']);
            
            if ($response['success'] && isset($response['data'])) {
                $transactions = [];
                foreach ($response['data'] as $item) {
                    $transactions[] = new Transaction($item);
                }
                return $transactions;
            }
            
            return [];
        } catch (\Exception $e) {
            error_log("Error getting counterparty transactions: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get display name
     */
    public function getDisplayName(): string
    {
        return $this->attributes['name'] ?? 'Unknown Counterparty';
    }
    
    /**
     * Get formatted contact info
     */
    public function getContactInfo(): string
    {
        $contact = [];
        
        if (!empty($this->attributes['email'])) {
            $contact[] = $this->attributes['email'];
        }
        
        if (!empty($this->attributes['phone'])) {
            $contact[] = $this->attributes['phone'];
        }
        
        return implode(' | ', $contact);
    }
    
    /**
     * Check if counterparty has email
     */
    public function hasEmail(): bool
    {
        return !empty($this->attributes['email']);
    }
    
    /**
     * Check if counterparty has phone
     */
    public function hasPhone(): bool
    {
        return !empty($this->attributes['phone']);
    }
    
    /**
     * Get counterparty initials for avatar
     */
    public function getInitials(): string
    {
        $name = $this->getDisplayName();
        $words = explode(' ', $name);
        
        if (count($words) >= 2) {
            return strtoupper(substr($words[0], 0, 1) . substr($words[1], 0, 1));
        } else {
            return strtoupper(substr($name, 0, 2));
        }
    }
    
    /**
     * Get avatar color based on name
     */
    public function getAvatarColor(): string
    {
        $colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
        ];
        
        $name = $this->getDisplayName();
        $index = crc32($name) % count($colors);
        return $colors[abs($index)];
    }
    
    /**
     * Get counterparties with pagination
     */
    public static function paginate(int $page = 1, int $perPage = 20, string $search = ''): array
    {
        try {
            $filters = [
                'per_page' => $perPage
            ];

            if (!empty($search)) {
                $filters['search'] = $search;
            }

            $response = self::getClient()->getCounterparties($filters);

            if ($response['success'] && isset($response['data'])) {
                $counterparties = [];

                // Handle pagination structure
                $counterpartyData = [];
                if (isset($response['data']['data'])) {
                    // Paginated API response
                    $counterpartyData = $response['data']['data'];
                } elseif (is_array($response['data']) && !isset($response['data']['current_page'])) {
                    // Simple array response
                    $counterpartyData = $response['data'];
                }

                foreach ($counterpartyData as $item) {
                    if (is_array($item)) {
                        $counterparties[] = new static($item);
                    }
                }
                return $counterparties;
            }

            return [];
        } catch (\Exception $e) {
            error_log("Error getting paginated counterparties: " . $e->getMessage());
            return [];
        }
    }
}
