<?php

namespace Strix\ERP\Models;

use Strix\ERP\Core\Model;
use Strix\ERP\Core\Database;

class TaskStatus extends Model
{
    protected string $table = 'task_statuses';
    
    protected array $fillable = [
        'name', 'description', 'color', 'is_final', 'sort_order', 'is_active'
    ];
    
    protected array $casts = [
        'is_final' => 'boolean',
        'is_active' => 'boolean',
        'sort_order' => 'integer'
    ];

    public static function findByName(string $name): ?self
    {
        return self::findBy('name', $name);
    }

    public function getTasks(): array
    {
        return Task::where('status_id', $this->id);
    }

    public function getTaskCount(): int
    {
        return (int) Database::fetchColumn(
            "SELECT COUNT(*) FROM tasks WHERE status_id = ?",
            [$this->id]
        );
    }

    public function isActive(): bool
    {
        return (bool) $this->is_active;
    }

    public function isFinal(): bool
    {
        return (bool) $this->is_final;
    }

    public static function getActiveStatuses(): array
    {
        $results = Database::fetchAll(
            "SELECT * FROM task_statuses WHERE is_active = 1 ORDER BY sort_order, name"
        );
        
        $statuses = [];
        foreach ($results as $data) {
            $status = new self();
            foreach ($data as $key => $value) {
                $status->$key = $value;
            }
            $statuses[] = $status;
        }
        
        return $statuses;
    }

    public static function getAllWithStats(): array
    {
        $results = Database::fetchAll(
            "SELECT ts.*, COUNT(t.id) as task_count
             FROM task_statuses ts
             LEFT JOIN tasks t ON ts.id = t.status_id
             GROUP BY ts.id
             ORDER BY ts.sort_order, ts.name"
        );
        
        $statuses = [];
        foreach ($results as $data) {
            $status = new self();
            foreach ($data as $key => $value) {
                $status->$key = $value;
            }
            $statuses[] = $status;
        }
        
        return $statuses;
    }

    public static function getDefaultStatus(): ?self
    {
        return self::findByName('Нова');
    }
}
