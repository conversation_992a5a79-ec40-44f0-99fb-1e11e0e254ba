<?php

namespace Strix\ERP\Models;

use Strix\ERP\Core\Model;

class TaskComment extends Model
{
    protected string $table = 'task_comments';
    
    protected array $fillable = [
        'task_id', 'user_id', 'comment', 'is_internal'
    ];
    
    protected array $casts = [
        'is_internal' => 'boolean'
    ];

    public function getTask(): ?Task
    {
        if (!$this->task_id) {
            return null;
        }
        return Task::find($this->task_id);
    }

    public function getUser(): ?User
    {
        if (!$this->user_id) {
            return null;
        }
        return User::find($this->user_id);
    }

    public function getUserName(): string
    {
        $user = $this->getUser();
        return $user ? $user->getFullName() : 'Неизвестен потребител';
    }

    public function isInternal(): bool
    {
        return (bool) $this->is_internal;
    }

    public static function getCommentsForTask(int $taskId, bool $includeInternal = true): array
    {
        $sql = "SELECT tc.*, u.username, u.first_name, u.last_name
                FROM task_comments tc
                INNER JOIN users u ON tc.user_id = u.id
                WHERE tc.task_id = ?";
        
        $params = [$taskId];
        
        if (!$includeInternal) {
            $sql .= " AND tc.is_internal = FALSE";
        }
        
        $sql .= " ORDER BY tc.created_at";
        
        $results = Database::fetchAll($sql, $params);
        
        $comments = [];
        foreach ($results as $data) {
            $comment = new self();
            foreach ($data as $key => $value) {
                $comment->$key = $value;
            }
            $comments[] = $comment;
        }
        
        return $comments;
    }
}
