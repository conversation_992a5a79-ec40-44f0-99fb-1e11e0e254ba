<?php

namespace Strix\ERP\Models;

use Strix\ERP\Core\Model;
use Strix\ERP\Core\Database;
use Exception;

class UserNextcloudSettings extends Model
{
    protected string $table = 'user_nextcloud_settings';
    
    protected array $fillable = [
        'user_id',
        'server_url',
        'username',
        'password',
        'verify_ssl',
        'timeout',
        'default_folder',
        'auto_create_folders',
        'is_active',
        'last_tested_at',
        'last_test_result',
        'last_error_message',
        'created_by',
        'updated_by'
    ];
    
    protected array $hidden = ['password'];
    
    /**
     * Get user's active Nextcloud settings
     */
    public static function getActiveForUser(int $userId): ?self
    {
        $sql = "SELECT * FROM user_nextcloud_settings WHERE user_id = ? AND is_active = 1 LIMIT 1";
        $data = Database::fetchOne($sql, [$userId]);
        
        if (!$data) {
            return null;
        }
        
        $instance = new self();
        $instance->fill($data);
        return $instance;
    }
    
    /**
     * Get decrypted password for user
     */
    public function getDecryptedPassword(): ?string
    {
        if (!$this->password) {
            return null;
        }

        try {
            // Use base64 decoding instead of MySQL AES
            $decoded = base64_decode($this->password);
            if ($decoded === false) {
                return null;
            }
            return $decoded;
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Set encrypted password
     */
    public function setEncryptedPassword(string $password): void
    {
        // Use base64 encoding instead of MySQL AES
        $this->password = base64_encode($password);
    }
    
    /**
     * Test connection to Nextcloud server
     */
    public function testConnection(): array
    {
        try {
            if (!$this->server_url || !$this->username) {
                throw new Exception('Server URL and username are required');
            }
            
            $decryptedPassword = $this->getDecryptedPassword();
            if (!$decryptedPassword) {
                throw new Exception('Password is required');
            }
            
            // Create a temporary Nextcloud client for testing
            $config = [
                'server' => [
                    'url' => $this->server_url,
                    'webdav_path' => '/remote.php/dav/files/',
                    'timeout' => $this->timeout ?: 30,
                    'verify_ssl' => $this->verify_ssl ?? true,
                ],
                'auth' => [
                    'method' => 'app_password',
                    'username' => $this->username,
                    'password' => $decryptedPassword,
                ]
            ];
            
            // Test basic connection by listing root directory
            $ch = curl_init();
            $url = rtrim($this->server_url, '/') . '/remote.php/dav/files/' . $this->username . '/';
            
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_CUSTOMREQUEST => 'PROPFIND',
                CURLOPT_HTTPHEADER => [
                    'Authorization: Basic ' . base64_encode($this->username . ':' . $decryptedPassword),
                    'Content-Type: application/xml',
                    'Depth: 1'
                ],
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => $this->timeout ?: 30,
                CURLOPT_SSL_VERIFYPEER => $this->verify_ssl ?? true,
                CURLOPT_POSTFIELDS => '<?xml version="1.0"?>
                    <d:propfind xmlns:d="DAV:">
                        <d:prop>
                            <d:displayname />
                        </d:prop>
                    </d:propfind>'
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                throw new Exception("Connection error: $error");
            }
            
            if ($httpCode >= 400) {
                throw new Exception("HTTP error: $httpCode");
            }
            
            // Update test results
            $this->updateTestResult('success', null);
            
            return [
                'success' => true,
                'message' => 'Connection successful',
                'http_code' => $httpCode
            ];
            
        } catch (Exception $e) {
            // Update test results
            $this->updateTestResult('failed', $e->getMessage());
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Update test result
     */
    private function updateTestResult(string $result, ?string $errorMessage = null): void
    {
        $sql = "UPDATE user_nextcloud_settings 
                SET last_tested_at = NOW(), 
                    last_test_result = ?, 
                    last_error_message = ?
                WHERE id = ?";
        
        Database::query($sql, [$result, $errorMessage, $this->id]);
        
        $this->last_tested_at = date('Y-m-d H:i:s');
        $this->last_test_result = $result;
        $this->last_error_message = $errorMessage;
    }
    
    /**
     * Create or update user settings
     */
    public static function createOrUpdate(int $userId, array $data): self
    {
        // Deactivate existing settings
        Database::query(
            "UPDATE user_nextcloud_settings SET is_active = 0 WHERE user_id = ?",
            [$userId]
        );
        
        // Create new settings
        $settings = new self();
        $settings->user_id = $userId;
        $settings->is_active = true;
        
        // Set basic fields
        foreach (['server_url', 'username', 'verify_ssl', 'timeout', 'default_folder', 'auto_create_folders'] as $field) {
            if (isset($data[$field])) {
                $settings->$field = $data[$field];
            }
        }
        
        // Handle password encryption
        if (!empty($data['password'])) {
            $settings->setEncryptedPassword($data['password']);
        }
        
        $settings->save();
        
        return $settings;
    }
    
    /**
     * Get all settings for a user (including inactive)
     */
    public static function getAllForUser(int $userId): array
    {
        $sql = "SELECT * FROM user_nextcloud_settings WHERE user_id = ? ORDER BY created_at DESC";
        $results = Database::fetchAll($sql, [$userId]);
        
        $settings = [];
        foreach ($results as $data) {
            $instance = new self();
            $instance->fill($data);
            $settings[] = $instance;
        }
        
        return $settings;
    }
    
    /**
     * Delete user settings
     */
    public function deleteSettings(): bool
    {
        if (!$this->id) {
            return false;
        }
        
        $sql = "DELETE FROM user_nextcloud_settings WHERE id = ?";
        $stmt = Database::query($sql, [$this->id]);
        return $stmt->rowCount() > 0;
    }
    
    /**
     * Check if user has valid Nextcloud configuration
     */
    public static function hasValidConfig(int $userId): bool
    {
        $settings = self::getActiveForUser($userId);
        
        if (!$settings) {
            return false;
        }
        
        return !empty($settings->server_url) && 
               !empty($settings->username) && 
               !empty($settings->password);
    }
    
    /**
     * Get configuration array for NextcloudClient
     */
    public function getClientConfig(): array
    {
        $decryptedPassword = $this->getDecryptedPassword();
        
        return [
            'server' => [
                'url' => $this->server_url,
                'webdav_path' => '/remote.php/dav/files/',
                'timeout' => $this->timeout ?: 30,
                'verify_ssl' => $this->verify_ssl ?? true,
            ],
            'auth' => [
                'method' => 'app_password',
                'username' => $this->username,
                'password' => $decryptedPassword,
            ],
            'files' => [
                'default_upload_folder' => $this->default_folder ?: '/ERP_Files',
            ]
        ];
    }
    
    /**
     * Get user who owns these settings
     */
    public function getUser(): ?User
    {
        if (!$this->user_id) {
            return null;
        }
        
        return User::find($this->user_id);
    }
    
    /**
     * Before save hook
     */
    protected function beforeSave(): void
    {
        parent::beforeSave();
        
        // Set default values
        if ($this->verify_ssl === null) {
            $this->verify_ssl = true;
        }
        
        if ($this->timeout === null) {
            $this->timeout = 30;
        }
        
        if (empty($this->default_folder)) {
            $this->default_folder = '/ERP_Files';
        }
        
        if ($this->auto_create_folders === null) {
            $this->auto_create_folders = true;
        }
        
        // Ensure only one active setting per user
        if ($this->is_active) {
            Database::query(
                "UPDATE user_nextcloud_settings SET is_active = 0 WHERE user_id = ? AND id != ?",
                [$this->user_id, $this->id ?: 0]
            );
        }
    }
}
