<?php

namespace Strix\ERP\Core;

class Application
{
    private array $config;
    private static ?Application $instance = null;

    public function __construct()
    {
        self::$instance = $this;
        $this->loadConfig();
        $this->initializeSession();
        $this->setTimezone();
    }

    public static function getInstance(): self
    {
        if (self::$instance === null) {
            new self();
        }
        
        return self::$instance;
    }

    private function loadConfig(): void
    {
        $this->config = require __DIR__ . '/../../config/app.php';
        Database::setConfig(require __DIR__ . '/../../config/database.php');
    }

    private function initializeSession(): void
    {
        if (session_status() === PHP_SESSION_NONE) {
            $sessionConfig = $this->config['session'];
            
            session_set_cookie_params([
                'lifetime' => $sessionConfig['lifetime'],
                'path' => $sessionConfig['path'],
                'domain' => $sessionConfig['domain'],
                'secure' => $sessionConfig['secure'],
                'httponly' => $sessionConfig['httponly'],
                'samesite' => $sessionConfig['samesite']
            ]);
            
            session_name($sessionConfig['name']);
            session_start();
            
            // Regenerate session ID periodically for security
            if (!isset($_SESSION['last_regeneration'])) {
                $_SESSION['last_regeneration'] = time();
            } elseif (time() - $_SESSION['last_regeneration'] > $this->config['security']['session_regenerate_interval']) {
                session_regenerate_id(true);
                $_SESSION['last_regeneration'] = time();
            }
        }
    }

    private function setTimezone(): void
    {
        date_default_timezone_set($this->config['timezone']);
    }

    public function getConfig(string $key = null): mixed
    {
        if ($key === null) {
            return $this->config;
        }
        
        return $this->config[$key] ?? null;
    }

    public function isDebug(): bool
    {
        return $this->config['debug'] ?? false;
    }

    public function handleError(\Throwable $e): void
    {
        if ($this->isDebug()) {
            echo '<pre>';
            echo "Error: " . $e->getMessage() . "\n";
            echo "File: " . $e->getFile() . "\n";
            echo "Line: " . $e->getLine() . "\n";
            echo "Trace:\n" . $e->getTraceAsString();
            echo '</pre>';
        } else {
            error_log($e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
            http_response_code(500);
            echo 'Възникна грешка. Моля опитайте отново.';
        }
    }

    public function redirect(string $url): void
    {
        header("Location: $url");
        exit;
    }

    public function setFlashMessage(string $type, string $message): void
    {
        $_SESSION['flash_messages'][] = [
            'type' => $type,
            'message' => $message
        ];
    }

    public function getFlashMessages(): array
    {
        $messages = $_SESSION['flash_messages'] ?? [];
        unset($_SESSION['flash_messages']);
        return $messages;
    }

    public function getCurrentUser(): ?array
    {
        return $_SESSION['user'] ?? null;
    }

    public function setCurrentUser(array $user): void
    {
        $_SESSION['user'] = $user;
    }

    public function logout(): void
    {
        session_destroy();
        session_start();
    }

    public function isLoggedIn(): bool
    {
        return isset($_SESSION['user']);
    }

    public function hasPermission(string $permission): bool
    {
        $user = $this->getCurrentUser();
        if (!$user) {
            return false;
        }

        return in_array($permission, $user['permissions'] ?? []);
    }
}
