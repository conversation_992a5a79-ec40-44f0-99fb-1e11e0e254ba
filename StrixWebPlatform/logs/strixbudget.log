[2025-06-18 21:58:55] [StrixBudget] [debug] Request: GET http://localhost:8000/api/auth/statistics (HTTP 200)
[2025-06-18 21:58:55] [StrixBudget] [debug] Request: GET http://localhost:8000/api/bank-accounts (HTTP 200)
[2025-06-18 21:58:55] [StrixBudget] [debug] Request: GET http://localhost:8000/api/transactions?per_page=10 (HTTP 200)
[2025-06-18 22:11:51] [StrixBudget] [debug] Request: POST http://localhost:8000/api/auth/login (HTTP 200)
[2025-06-18 22:11:51] [StrixBudget] [debug] Request: POST http://localhost:8000/api/auth/login (HTTP 200)
[2025-06-18 22:12:11] [StrixBudget] [debug] Request: POST http://localhost:8000/api/auth/login (HTTP 200)
[2025-06-18 22:12:11] [StrixBudget] [debug] Request: GET http://localhost:8000/api/auth/statistics (HTTP 200)
[2025-06-18 22:12:11] [StrixBudget] [debug] Request: GET http://localhost:8000/api/bank-accounts (HTTP 200)
[2025-06-18 22:12:11] [StrixBudget] [debug] Request: GET http://localhost:8000/api/transactions?per_page=10 (HTTP 200)
[2025-06-18 22:12:17] [StrixBudget] [debug] Request: POST http://localhost:8000/api/auth/login (HTTP 200)
[2025-06-18 22:12:56] [StrixBudget] [debug] Request: POST http://localhost:8000/api/auth/login (HTTP 200)
[2025-06-18 22:12:56] [StrixBudget] [debug] Request: GET http://localhost:8000/api/auth/statistics (HTTP 200)
[2025-06-18 22:12:56] [StrixBudget] [debug] Request: GET http://localhost:8000/api/bank-accounts (HTTP 200)
[2025-06-18 22:12:56] [StrixBudget] [debug] Request: GET http://localhost:8000/api/transactions?per_page=10 (HTTP 200)
[2025-06-18 22:16:28] [StrixBudget] [debug] Request: POST http://localhost:8000/api/auth/login (HTTP 200)
[2025-06-18 22:16:28] [StrixBudget] [debug] Request: GET http://localhost:8000/api/auth/statistics (HTTP 200)
[2025-06-18 22:16:28] [StrixBudget] [debug] Request: GET http://localhost:8000/api/bank-accounts (HTTP 200)
[2025-06-18 22:16:28] [StrixBudget] [debug] Request: GET http://localhost:8000/api/transactions?per_page=10 (HTTP 200)
[2025-06-18 22:17:56] [StrixBudget] [debug] Request: POST http://localhost:8000/api/auth/login (HTTP 200)
[2025-06-18 22:18:13] [StrixBudget] [debug] Request: POST http://localhost:8000/api/auth/login (HTTP 200)
[2025-06-18 19:47:21] [StrixBudget] [info] Mock mode disabled
[2025-06-18 19:47:21] [StrixBudget] [info] Testing API connection...
[2025-06-18 19:48:02] [StrixBudget] [info] Mock mode disabled
[2025-06-18 19:48:02] [StrixBudget] [info] Testing API connection...
[2025-06-18 19:48:02] [StrixBudget] [info] Mock mode disabled
[2025-06-18 19:49:02] [StrixBudget] [info] Mock mode disabled
[2025-06-18 19:49:02] [StrixBudget] [info] Testing API connection...
[2025-06-18 19:49:02] [StrixBudget] [info] Mock mode disabled
[2025-06-18 19:49:33] [StrixBudget] [info] Mock mode disabled
[2025-06-18 19:49:33] [StrixBudget] [info] Testing API connection...
[2025-06-18 19:49:33] [StrixBudget] [info] Mock mode disabled
[2025-06-18 19:50:37] [StrixBudget] [info] Mock mode disabled
[2025-06-18 19:50:37] [StrixBudget] [info] Testing API connection...
[2025-06-18 19:50:37] [StrixBudget] [info] Mock mode disabled
[2025-06-18 22:57:07] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 22:57:07] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 22:57:07] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 22:57:07] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 22:57:07] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 22:57:07] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 22:57:07] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 22:57:07] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 22:57:08] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:00:29] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:00:29] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:00:29] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:00:29] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/auth/login
[2025-06-18 23:00:29] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:00:29] [StrixBudget] [debug] Response received: HTTP 404, 9192 bytes
[2025-06-18 23:00:29] [StrixBudget] [error] HTTP error 404 for POST http://localhost:8000/auth/login. Response: {
    "message": "The route auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/home/<USER>/git/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/home/<USER>/git/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/RouteCollection.php",
            "line": 162,
            "function": "handleMatchedRoute",
      
[2025-06-18 23:00:39] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:00:39] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:00:39] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:00:39] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:00:39] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:00:39] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:00:58] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:00:58] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:00:59] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:00:59] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:00:59] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:00:59] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:00:59] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:00:59] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:00:59] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:01:06] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:01:06] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:01:06] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:01:06] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/auth/user
[2025-06-18 23:01:06] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/auth/user
[2025-06-18 23:01:11] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:01:11] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:01:11] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:01:11] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/bank-accounts
[2025-06-18 23:01:11] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/bank-accounts
[2025-06-18 23:01:37] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:01:37] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:01:37] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:01:37] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/transactions?limit=10
[2025-06-18 23:01:38] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/transactions?limit=10
[2025-06-18 23:01:39] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:01:39] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:01:39] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:01:39] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/auth/statistics
[2025-06-18 23:01:39] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/auth/statistics
[2025-06-18 23:01:41] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:01:41] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:01:41] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:01:41] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/auth/user
[2025-06-18 23:01:41] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/auth/user
[2025-06-18 23:01:41] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:01:41] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:01:41] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:01:41] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/bank-accounts
[2025-06-18 23:01:41] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/bank-accounts
[2025-06-18 23:01:41] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:01:41] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:01:41] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:01:41] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/transactions?limit=5
[2025-06-18 23:01:41] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/transactions?limit=5
[2025-06-18 23:01:41] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:01:41] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:01:42] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:01:42] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/auth/statistics
[2025-06-18 23:01:42] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/auth/statistics
[2025-06-18 23:01:55] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:01:55] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:01:56] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:01:56] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/counterparties
[2025-06-18 23:01:56] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/counterparties
[2025-06-18 23:02:17] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:02:17] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:02:18] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:02:18] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:02:18] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:02:18] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:02:18] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:02:18] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:02:18] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:04:02] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:04:02] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:04:02] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:04:02] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:04:02] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:04:02] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:04:02] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:04:02] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:04:02] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:04:02] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:04:02] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:04:30] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:04:30] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:04:30] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:04:30] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:04:30] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:04:30] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:04:30] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:04:30] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:04:31] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:04:31] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:04:31] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:05:24] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=20
[2025-06-18 23:05:24] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:05:24] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/transactions?per_page=20. Response: {"message":"Unauthenticated."}
[2025-06-18 23:05:24] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:05:24] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:05:24] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/bank-accounts. Response: {"message":"Unauthenticated."}
[2025-06-18 23:05:26] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:05:26] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:05:27] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:05:27] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:05:27] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:05:27] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:05:27] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:05:27] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:05:27] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:05:27] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:05:27] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:05:30] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:05:30] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:05:30] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/bank-accounts. Response: {"message":"Unauthenticated."}
[2025-06-18 23:05:32] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:05:32] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:05:32] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:05:32] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:05:32] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:05:32] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:05:32] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:05:32] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:05:33] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:05:33] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:05:33] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:05:36] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:05:36] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:05:37] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:05:37] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:05:37] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:05:37] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:05:37] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:05:37] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:05:37] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:05:37] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:05:37] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:05:48] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:05:48] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:05:48] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/bank-accounts. Response: {"message":"Unauthenticated."}
[2025-06-18 23:05:52] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:05:52] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:05:52] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:05:52] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:05:52] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:05:52] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:05:52] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:05:52] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:05:52] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:05:52] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:05:52] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:07:40] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:07:40] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:07:40] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:07:40] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:07:40] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:07:40] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:07:40] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:07:40] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:07:40] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:07:40] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:07:41] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:08:25] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:08:25] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:08:26] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:08:26] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:08:26] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:08:26] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:08:26] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:08:26] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:08:26] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:08:26] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:08:26] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:08:28] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:08:28] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:08:28] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/bank-accounts. Response: {"message":"Unauthenticated."}
[2025-06-18 23:08:28] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:08:28] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:08:28] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/transfers. Response: {"message":"Unauthenticated."}
[2025-06-18 23:08:30] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:08:30] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:08:30] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:08:30] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:08:30] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:08:30] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:08:30] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:08:30] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:08:31] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:08:31] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:08:31] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 20:08:39] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/transactions?per_page=5
[2025-06-18 20:08:39] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/transactions?per_page=5
[2025-06-18 20:08:39] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/bank-accounts
[2025-06-18 20:08:39] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/bank-accounts
[2025-06-18 20:08:39] [StrixBudget] [info] Mock mode enabled
[2025-06-18 20:08:39] [StrixBudget] [debug] Using mock mode for GET /transactions?per_page=2
[2025-06-18 20:08:39] [StrixBudget] [info] Mock mode disabled
[2025-06-18 20:08:39] [StrixBudget] [debug] Making real API request: GET http://invalid-url/transactions?per_page=1
[2025-06-18 20:08:39] [StrixBudget] [error] cURL error: Could not resolve host: invalid-url for GET http://invalid-url/transactions?per_page=1
[2025-06-18 23:08:51] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:08:51] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:08:52] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:08:52] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:08:52] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:08:52] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:08:52] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:08:52] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:08:52] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:08:52] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:08:52] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:08:58] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:08:58] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:08:58] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:08:58] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:08:58] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:08:58] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:08:58] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:08:58] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:08:58] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:08:58] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:08:58] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 20:10:13] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/auth/statistics
[2025-06-18 20:10:13] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/auth/statistics
[2025-06-18 20:10:13] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/bank-accounts
[2025-06-18 20:10:13] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/bank-accounts
[2025-06-18 20:10:13] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/transactions?per_page=10
[2025-06-18 20:10:13] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/transactions?per_page=10
[2025-06-18 20:10:13] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/transactions?per_page=10
[2025-06-18 20:10:13] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/transactions?per_page=10
[2025-06-18 23:10:42] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:10:42] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:10:43] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:10:43] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:10:43] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:10:43] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:10:43] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:10:43] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:10:43] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:10:43] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:10:43] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:10:50] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:10:50] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:10:51] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:10:51] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:10:51] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:10:51] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:10:51] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:10:51] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:10:51] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:10:51] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:10:51] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:10:52] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:10:52] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:10:52] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/bank-accounts. Response: {"message":"Unauthenticated."}
[2025-06-18 23:10:55] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:10:55] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:10:56] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:10:56] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:10:56] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:10:56] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:10:56] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:10:56] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:10:56] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:10:56] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:10:56] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:10:57] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:10:57] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:10:57] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/bank-accounts. Response: {"message":"Unauthenticated."}
[2025-06-18 23:10:57] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:10:57] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:10:57] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/transfers. Response: {"message":"Unauthenticated."}
[2025-06-18 23:10:59] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:10:59] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:10:59] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:10:59] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:10:59] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:10:59] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:10:59] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:10:59] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:10:59] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:10:59] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:10:59] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:11:03] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:11:03] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:11:03] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:11:03] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:11:03] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:11:03] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:11:03] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:11:03] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:11:03] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:11:03] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:11:03] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:11:06] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:11:06] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:11:07] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:11:07] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:11:07] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:11:07] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:11:07] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:11:07] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:11:07] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:11:07] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:11:07] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:11:10] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=20
[2025-06-18 23:11:10] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:11:10] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/transactions?per_page=20. Response: {"message":"Unauthenticated."}
[2025-06-18 23:11:10] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:11:10] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:11:10] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/bank-accounts. Response: {"message":"Unauthenticated."}
[2025-06-18 23:11:11] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:11:11] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:11:12] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:11:12] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:11:12] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:11:12] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:11:12] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:11:12] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:11:12] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:11:12] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:11:12] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:11:19] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:11:19] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:11:19] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:11:19] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:11:19] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:11:19] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:11:19] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:11:19] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:11:19] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:11:19] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:11:19] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:13:00] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:13:00] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:13:00] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/bank-accounts. Response: {"message":"Unauthenticated."}
[2025-06-18 23:15:17] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:15:17] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:15:17] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:15:17] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:15:17] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:15:17] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:15:17] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:15:17] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:15:17] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:15:17] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:15:17] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:19:19] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:19:19] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:19:19] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/bank-accounts. Response: {"message":"Unauthenticated."}
[2025-06-18 23:19:19] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/counterparties
[2025-06-18 23:19:19] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:19:19] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/counterparties. Response: {"message":"Unauthenticated."}
[2025-06-18 23:19:19] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transaction-types
[2025-06-18 23:19:19] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:19:19] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/transaction-types. Response: {"message":"Unauthenticated."}
[2025-06-18 23:19:24] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/counterparties?per_page=20
[2025-06-18 23:19:24] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:19:24] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/counterparties?per_page=20. Response: {"message":"Unauthenticated."}
[2025-06-18 23:19:29] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:19:29] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:19:29] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/transfers. Response: {"message":"Unauthenticated."}
[2025-06-18 23:21:18] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transaction-types
[2025-06-18 23:21:18] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:21:18] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/transaction-types. Response: {"message":"Unauthenticated."}
[2025-06-18 23:21:24] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:21:24] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:21:24] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/bank-accounts. Response: {"message":"Unauthenticated."}
[2025-06-18 23:22:35] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:22:35] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:22:35] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:22:35] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:22:35] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:22:35] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:22:35] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:22:35] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:22:35] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:22:35] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:22:35] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:22:37] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:22:37] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:22:37] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/bank-accounts. Response: {"message":"Unauthenticated."}
[2025-06-18 23:22:37] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/counterparties
[2025-06-18 23:22:37] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:22:37] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/counterparties. Response: {"message":"Unauthenticated."}
[2025-06-18 23:22:37] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transaction-types
[2025-06-18 23:22:37] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:22:37] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/transaction-types. Response: {"message":"Unauthenticated."}
[2025-06-18 23:22:42] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:22:42] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:22:42] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:22:42] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:22:42] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:22:42] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:22:42] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:22:42] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:22:42] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:22:42] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:22:42] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:22:49] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-18 23:22:49] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-18 23:22:50] [StrixBudget] [debug] Response received: HTTP 200, 436 bytes
[2025-06-18 23:22:50] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/auth/statistics
[2025-06-18 23:22:50] [StrixBudget] [debug] Response received: HTTP 200, 291 bytes
[2025-06-18 23:22:50] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/bank-accounts
[2025-06-18 23:22:50] [StrixBudget] [debug] Response received: HTTP 200, 1933 bytes
[2025-06-18 23:22:50] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transactions?per_page=10
[2025-06-18 23:22:50] [StrixBudget] [debug] Response received: HTTP 200, 13747 bytes
[2025-06-18 23:22:50] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transfers
[2025-06-18 23:22:50] [StrixBudget] [debug] Response received: HTTP 200, 2930 bytes
[2025-06-18 23:23:18] [StrixBudget] [debug] Making real API request: GET http://localhost:8000/api/transaction-types
[2025-06-18 23:23:18] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-18 23:23:18] [StrixBudget] [error] HTTP error 401 for GET http://localhost:8000/api/transaction-types. Response: {"message":"Unauthenticated."}
[2025-06-19 17:45:59] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-19 17:45:59] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:46:29] [StrixBudget] [error] cURL error: Operation timed out after 30001 milliseconds with 0 bytes received for POST http://localhost:8000/api/auth/login
[2025-06-19 17:46:59] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-19 17:46:59] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:47:29] [StrixBudget] [error] cURL error: Operation timed out after 30001 milliseconds with 0 bytes received for POST http://localhost:8000/api/auth/login
[2025-06-19 17:48:35] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-19 17:48:35] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:49:05] [StrixBudget] [error] cURL error: Operation timed out after 30000 milliseconds with 0 bytes received for POST http://localhost:8000/api/auth/login
[2025-06-19 17:49:05] [StrixBudget] [debug] Making real API request: POST http://localhost:8000/api/auth/login
[2025-06-19 17:49:05] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:49:35] [StrixBudget] [error] cURL error: Operation timed out after 30001 milliseconds with 0 bytes received for POST http://localhost:8000/api/auth/login
[2025-06-19 17:49:35] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:49:35] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:49:36] [StrixBudget] [debug] Response received: HTTP 200, 368 bytes
[2025-06-19 17:49:36] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:49:36] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:49:36] [StrixBudget] [debug] Response received: HTTP 200, 368 bytes
[2025-06-19 17:49:56] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:49:56] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:49:56] [StrixBudget] [debug] Response received: HTTP 200, 368 bytes
[2025-06-19 17:49:56] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:49:56] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:49:56] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:49:56] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:49:56] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:49:56] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:49:56] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:49:56] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:50:02] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:50:02] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:50:02] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 17:50:02] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 17:50:02] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 17:50:02] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 17:50:05] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:50:05] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:50:06] [StrixBudget] [debug] Response received: HTTP 200, 368 bytes
[2025-06-19 17:50:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:50:06] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:50:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:50:06] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:50:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:50:06] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:50:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:50:06] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:50:07] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:50:07] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:50:10] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:50:10] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:50:10] [StrixBudget] [debug] Response received: HTTP 200, 368 bytes
[2025-06-19 17:50:10] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:50:10] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:50:10] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:50:10] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:50:10] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:50:10] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:50:10] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:50:10] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:50:14] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:50:14] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:50:15] [StrixBudget] [debug] Response received: HTTP 200, 368 bytes
[2025-06-19 17:50:15] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:50:15] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:50:15] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:50:15] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:50:15] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:50:15] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:50:15] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:50:15] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:50:18] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:50:18] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:50:18] [StrixBudget] [debug] Response received: HTTP 200, 368 bytes
[2025-06-19 17:50:18] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:50:18] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:50:18] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:50:18] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:50:18] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:50:18] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:50:18] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:50:18] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:50:20] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:50:20] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:50:20] [StrixBudget] [debug] Response received: HTTP 200, 368 bytes
[2025-06-19 17:50:23] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:50:23] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:50:23] [StrixBudget] [debug] Response received: HTTP 200, 368 bytes
[2025-06-19 17:50:23] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:50:23] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:50:23] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:50:23] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:50:23] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:50:23] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:50:23] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:50:23] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:50:53] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 17:50:53] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:50:53] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:50:53] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:50:53] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 17:50:53] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 17:50:53] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 17:50:54] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 17:50:57] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:50:57] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:50:57] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:50:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:50:57] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:50:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:50:57] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:50:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:50:57] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:50:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:50:57] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:51:02] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 17:51:02] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:51:02] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:51:02] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:51:02] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 17:51:02] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 17:51:02] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 17:51:02] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 17:51:03] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:51:03] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:51:04] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:51:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:51:04] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:51:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:51:04] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:51:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:51:04] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:51:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:51:04] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:52:37] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:52:37] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:52:37] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:52:49] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:52:49] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:52:49] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:52:59] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:52:59] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:52:59] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:52:59] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:52:59] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:52:59] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:52:59] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:52:59] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:52:59] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:52:59] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:52:59] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:53:02] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:53:02] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:53:02] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 17:53:02] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 17:53:02] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 17:53:02] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 17:53:04] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:53:04] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:53:04] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:53:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:53:04] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:53:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:53:04] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:53:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:53:04] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:53:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:53:04] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:53:05] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:53:05] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:53:07] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:53:07] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:53:07] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:53:07] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:53:07] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:53:07] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:53:07] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:53:07] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:53:07] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:53:07] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:53:07] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:55:04] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:55:04] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:55:04] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:55:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:55:04] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:55:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:55:04] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:55:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:55:04] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:55:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:55:04] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:55:07] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:55:07] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:55:08] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:55:08] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:55:08] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:55:08] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:55:08] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:55:08] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:55:08] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:55:08] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:55:08] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:55:09] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:55:09] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:55:16] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:55:16] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:55:16] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:55:16] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:55:16] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:55:16] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:55:16] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:55:16] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:55:16] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:55:16] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:55:16] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:55:21] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:55:21] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:55:21] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 17:55:21] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 17:55:21] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 17:55:21] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 17:55:24] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:55:24] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:55:25] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:55:25] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:55:25] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:55:25] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:55:25] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:55:25] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:55:25] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:55:25] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:55:25] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:57:00] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:57:00] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:57:02] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:57:02] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:57:03] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:57:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:57:03] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:57:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:57:03] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:57:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:57:03] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:57:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:57:03] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:57:15] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 17:57:16] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:57:16] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:57:16] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:57:16] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 17:57:16] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 17:57:16] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 17:57:16] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 17:57:24] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:57:24] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:57:24] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:57:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:57:24] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:57:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:57:24] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:57:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:57:24] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:57:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:57:24] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:57:28] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:57:28] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:57:38] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:57:38] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:57:38] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:57:38] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:57:38] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:57:38] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:57:38] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:57:38] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:57:38] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:57:38] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:57:38] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:57:43] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 17:57:43] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:57:43] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:57:43] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:57:43] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 17:57:43] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 17:57:43] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 17:57:43] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 17:58:09] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:58:09] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:58:09] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:58:09] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:58:09] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:58:09] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:58:09] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:58:09] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:58:09] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:58:09] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:58:09] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:58:21] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties?per_page=20
[2025-06-19 17:58:21] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 17:58:33] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:58:33] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:58:33] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:58:33] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:58:33] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:58:33] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:58:33] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:58:33] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:58:33] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:58:33] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:58:33] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 17:58:42] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 17:58:42] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 17:58:42] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 17:58:42] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 17:58:42] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 17:58:42] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 17:58:42] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 17:58:42] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 17:58:42] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 17:58:42] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 17:58:42] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:37:44] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:37:44] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 19:38:02] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:38:02] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:38:02] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:38:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 19:38:03] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:38:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:38:03] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 19:38:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 19:38:03] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 19:38:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 19:38:03] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 19:38:34] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:38:34] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:38:34] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:39:48] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:39:48] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:39:48] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:39:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 19:39:48] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 19:39:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:39:48] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 19:39:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 19:39:48] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:39:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:39:48] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:39:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:39:50] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 19:39:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 19:39:50] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 19:39:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 19:39:50] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 19:40:36] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:40:36] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 19:40:36] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 19:40:36] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 19:40:36] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 19:40:36] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 19:40:47] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:40:47] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 19:40:47] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 19:40:47] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 19:40:47] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 19:40:48] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 19:42:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:42:06] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 19:42:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 19:42:06] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 19:42:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 19:42:06] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 19:42:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:42:58] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 19:42:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 19:42:58] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 19:42:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 19:42:58] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 19:43:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 19:43:03] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:43:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:43:03] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 19:43:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 19:43:03] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 19:43:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 19:43:03] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 19:43:49] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:43:49] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:43:49] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:43:49] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 19:43:49] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 19:43:49] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:43:49] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 19:43:49] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 19:43:49] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:43:49] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:43:49] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:43:52] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:43:52] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 19:45:07] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:45:07] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 19:45:13] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:45:13] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 19:46:16] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:46:16] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:46:55] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:46:55] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:46:55] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:46:55] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 19:46:55] [StrixBudget] [debug] Response received: HTTP 200, 288 bytes
[2025-06-19 19:46:55] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:46:55] [StrixBudget] [debug] Response received: HTTP 200, 582 bytes
[2025-06-19 19:46:55] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 19:46:55] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:46:55] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:46:55] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:47:17] [StrixBudget] [debug] Making real API request: POST http://***************/api/bank-accounts
[2025-06-19 19:47:17] [StrixBudget] [debug] Request data: {"name":"Nova 1","currency":"BGN","balance":168,"is_active":true,"is_default":false}
[2025-06-19 19:47:17] [StrixBudget] [debug] Response received: HTTP 201, 311 bytes
[2025-06-19 19:47:17] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:47:17] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:47:41] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:47:41] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:47:41] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:47:41] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 19:47:41] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 19:47:41] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:47:41] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:47:41] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 19:47:41] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:47:41] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:47:41] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:48:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:48:24] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:48:28] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:48:28] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:48:28] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:48:28] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 19:48:28] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 19:48:28] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:48:28] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:48:28] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 19:48:28] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:48:28] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:48:28] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:48:36] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties?per_page=20
[2025-06-19 19:48:36] [StrixBudget] [debug] Response received: HTTP 200, 1239 bytes
[2025-06-19 19:49:27] [StrixBudget] [debug] Making real API request: POST http://***************/api/counterparties
[2025-06-19 19:49:27] [StrixBudget] [debug] Request data: {"name":"Nema takav","description":"","email":"","phone":""}
[2025-06-19 19:49:27] [StrixBudget] [debug] Response received: HTTP 201, 241 bytes
[2025-06-19 19:49:27] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties?per_page=20
[2025-06-19 19:49:27] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 19:50:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties?per_page=20
[2025-06-19 19:50:24] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 19:50:28] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:50:28] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:50:28] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:50:28] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 19:50:28] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 19:50:28] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:50:28] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:50:28] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 19:50:28] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:50:28] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:50:28] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:50:32] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 19:50:32] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:50:32] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:50:32] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:50:32] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 19:50:32] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 19:50:32] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 19:50:32] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 19:50:37] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:50:37] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:50:37] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:50:37] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 19:50:37] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 19:50:37] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:50:37] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:50:37] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 19:50:37] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:50:37] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:50:37] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:52:33] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:52:33] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:52:35] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:52:35] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:52:36] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:52:36] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 19:52:36] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 19:52:36] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:52:36] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:52:36] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 19:52:36] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:52:36] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:52:36] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:52:38] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 19:52:38] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:52:38] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:52:38] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:52:38] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 19:52:38] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 19:52:38] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 19:52:38] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 19:52:39] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:52:39] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:52:40] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:52:40] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 19:52:40] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 19:52:40] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:52:40] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:52:40] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 19:52:40] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:52:40] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:52:40] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:54:04] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:54:04] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:54:04] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:54:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 19:54:04] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 19:54:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:54:04] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:54:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 19:54:04] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:54:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:54:04] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:54:10] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 19:54:10] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:54:10] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:54:10] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:54:10] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 19:54:10] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 19:54:10] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 19:54:10] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 19:54:14] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:54:14] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:54:14] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:54:14] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 19:54:14] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 19:54:14] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:54:14] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:54:14] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 19:54:14] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:54:14] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:54:14] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:54:19] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:54:19] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:54:19] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:55:33] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:55:33] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:55:33] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:59:19] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:59:19] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:59:19] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 19:59:19] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 19:59:19] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 19:59:19] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 19:59:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:59:24] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:59:29] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:59:29] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:59:29] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 19:59:29] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 19:59:29] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 19:59:29] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:59:30] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:59:30] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 19:59:30] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:59:30] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:59:30] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 20:00:44] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 20:00:44] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 20:00:45] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 20:00:45] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 20:00:45] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 20:00:45] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 20:00:45] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 20:00:45] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 20:00:45] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 20:01:07] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 20:01:07] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 20:01:07] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 20:01:07] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 20:01:07] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 20:01:07] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 20:01:07] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 20:01:07] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 20:01:07] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 21:06:30] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:06:30] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:06:30] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:06:30] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:06:30] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:06:30] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:06:30] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:06:30] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:06:30] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:06:30] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:06:30] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:35:11] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:35:11] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:35:11] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:35:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:35:11] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:35:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:35:12] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:35:12] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:35:12] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:35:12] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:35:12] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:38:32] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:38:32] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:38:33] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:38:45] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:38:45] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:38:45] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:39:11] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:39:11] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:39:11] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:39:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:39:11] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:39:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:39:11] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:39:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:39:11] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:39:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:39:11] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:43:48] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:43:48] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:43:48] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:43:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:43:48] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:43:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:43:48] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:43:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:43:48] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:43:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:43:48] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:46:10] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:46:10] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:46:10] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:46:10] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:46:10] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:46:10] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:46:10] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:46:10] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:46:10] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:46:10] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:46:10] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:46:57] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:46:57] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:46:58] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:46:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:46:58] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:46:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:46:58] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:46:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:46:58] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:46:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:46:58] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:48:35] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:48:35] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:48:35] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:48:35] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:48:35] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:48:35] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:48:35] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:48:35] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:48:35] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:48:35] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:48:35] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:49:32] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:32] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:32] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:32] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:49:32] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:49:32] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 21:49:32] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 21:49:32] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 21:49:32] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 21:49:34] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:34] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:34] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:34] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:49:34] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:49:34] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:49:34] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:49:34] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:49:34] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:49:34] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:49:34] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:49:36] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:36] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:36] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:36] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:49:36] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:49:37] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:37] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:38] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:38] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:49:38] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:49:38] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:49:38] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:49:38] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:49:38] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:49:38] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:49:38] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:49:39] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:39] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:39] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:40] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:40] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:41] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:41] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:49:41] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:49:41] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:49:41] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:49:41] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:49:41] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:49:41] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:49:41] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:49:42] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:42] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:42] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:43] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:43] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:44] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:44] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:49:44] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:49:44] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:49:44] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:49:44] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:49:44] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:49:44] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:49:44] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:49:46] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:46] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:46] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:47] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:47] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:47] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:47] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:49:47] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:49:47] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:49:47] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:49:47] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:49:47] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:49:47] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:49:48] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:49:50] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:50] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:50] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 21:49:50] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:49:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:49:50] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:49:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 21:49:50] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 21:49:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 21:49:50] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 21:49:52] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:52] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:53] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:53] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:49:53] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:49:53] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:49:53] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:49:53] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:49:53] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:49:53] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:49:53] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:49:54] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:54] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:55] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:55] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:49:55] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:49:56] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:56] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:56] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:56] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:49:57] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:49:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:49:57] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:49:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:49:57] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:49:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:49:57] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:49:58] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:49:58] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:49:59] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:49:59] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties?per_page=20
[2025-06-19 21:49:59] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 21:50:01] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:50:01] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:50:01] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:50:01] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:50:01] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:50:01] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:50:01] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:50:01] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:50:01] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:50:01] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:50:01] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:50:02] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:50:02] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:50:03] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:50:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 21:50:03] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 21:50:04] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:50:04] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:50:04] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:50:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:50:04] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:50:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:50:04] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:50:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:50:04] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:50:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:50:04] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:52:26] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:52:26] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:52:26] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:52:26] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:52:26] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:52:26] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:52:26] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:52:26] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:52:26] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:52:26] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:52:26] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:52:41] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:52:41] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:52:42] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:52:42] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:52:42] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:52:42] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:52:42] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:52:42] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:52:42] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:52:42] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:52:42] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:58:05] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:58:05] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:58:06] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:58:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:58:06] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:58:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:58:06] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:58:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:58:06] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:58:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:58:06] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:58:31] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:58:31] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:58:31] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:58:31] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:58:31] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:58:31] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:58:31] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:58:31] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:58:31] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:58:31] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:58:31] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 21:58:45] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 21:58:45] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 21:58:46] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 21:58:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 21:58:46] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 21:58:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 21:58:46] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 21:58:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 21:58:46] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 21:58:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 21:58:46] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:00:37] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:00:37] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:00:37] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:00:37] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 22:00:37] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:00:37] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:00:37] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:00:37] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 22:00:37] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:00:37] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:00:37] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 22:04:06] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:04:06] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:04:06] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:04:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:04:06] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:04:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:04:06] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:04:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:04:06] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:04:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:04:06] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:04:44] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:04:44] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:04:44] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:04:44] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:04:44] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:04:44] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:04:44] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:04:44] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:04:44] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:04:44] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:04:44] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:06:10] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:06:10] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:06:11] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:06:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:06:11] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:07:12] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:07:12] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:07:12] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:07:12] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:07:12] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:07:12] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:07:12] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:07:12] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:07:12] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:07:12] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:07:12] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:07:24] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:07:24] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:07:24] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:07:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:07:24] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:07:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:07:24] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:07:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:07:24] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:07:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:07:24] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:07:45] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:07:45] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:07:45] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:07:45] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:07:46] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:07:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:07:46] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:07:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:07:46] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:07:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:07:46] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:07:51] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:07:51] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:07:52] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:07:52] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:07:52] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:07:52] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:07:52] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:07:52] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:07:52] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:07:52] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:07:52] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:08:23] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:08:23] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:08:23] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:08:23] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:08:23] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:08:23] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:08:23] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:08:23] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:08:23] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:08:23] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:08:23] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:08:48] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:08:48] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:08:48] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:08:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:08:48] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:08:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:08:48] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:08:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:08:48] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:08:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:08:48] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:08:51] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:08:51] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:08:51] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:08:51] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:08:52] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:08:52] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:08:52] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:08:52] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:08:52] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:08:52] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:08:52] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:12:30] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:12:30] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:12:31] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:12:31] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:12:31] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:12:31] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:12:31] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:12:31] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:12:31] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:12:31] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:12:31] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:12:44] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:12:44] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:12:45] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:12:45] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 22:12:45] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:12:45] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:12:45] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:12:45] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 22:12:45] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:12:45] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:12:45] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 22:12:48] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:12:48] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:12:48] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:12:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:12:48] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:12:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:12:48] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:12:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:12:48] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:12:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:12:48] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:13:07] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:13:07] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:13:07] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:13:07] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:13:07] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:13:07] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:13:07] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:13:07] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:13:07] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:13:07] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:13:07] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:13:27] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:13:27] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:13:27] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:13:27] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:13:27] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:13:27] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:13:27] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:13:27] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:13:27] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:13:27] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:13:27] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:13:45] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:13:45] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:13:46] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:13:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:13:46] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:13:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:13:46] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:13:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:13:46] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:13:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:13:46] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:13:51] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:13:51] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:13:51] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:13:59] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:13:59] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:14:00] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:14:11] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:14:11] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:14:11] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:14:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:14:11] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:14:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:14:11] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:14:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:14:11] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:14:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:14:11] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:14:33] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:14:33] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:14:33] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:14:53] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:14:53] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:14:53] [StrixBudget] [debug] Response received: HTTP 200, 369 bytes
[2025-06-19 22:14:53] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:14:53] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:14:54] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:14:54] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:14:54] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:14:54] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:15:02] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:15:02] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:15:02] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:15:02] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:15:02] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:15:02] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:15:02] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:15:02] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:15:02] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:15:02] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:15:02] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:15:12] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:15:12] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:15:13] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:15:13] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:15:13] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:15:13] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 22:15:13] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:15:13] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:15:13] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 22:15:20] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:15:20] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:15:21] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:15:21] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 22:15:21] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:15:21] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:15:21] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:15:21] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 22:15:21] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:15:21] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:15:21] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 22:15:26] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:15:26] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:15:26] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:15:26] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:15:26] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:15:26] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:15:26] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:15:26] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:15:26] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:15:26] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:15:26] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:15:31] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:15:31] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:15:31] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:15:31] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties?per_page=20
[2025-06-19 22:15:31] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:17:55] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:17:55] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:17:56] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:17:56] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties?per_page=20
[2025-06-19 22:17:56] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:18:35] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:18:35] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:18:35] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:18:35] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties?per_page=20
[2025-06-19 22:18:35] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:18:42] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:18:42] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:18:42] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:18:42] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:18:42] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:18:42] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:18:42] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:18:42] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:18:42] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:18:42] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:18:42] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:18:48] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:18:48] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:18:49] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:18:49] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties?per_page=20
[2025-06-19 22:18:49] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:19:06] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:19:06] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:19:06] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:19:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:19:06] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:19:33] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:19:33] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:19:33] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:19:33] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:19:33] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:19:33] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:19:33] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:19:33] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:19:33] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:19:33] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:19:33] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:22:29] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:22:29] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:22:29] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:22:29] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transfers
[2025-06-19 22:22:29] [StrixBudget] [debug] Response received: HTTP 404, 8829 bytes
[2025-06-19 22:22:29] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transfers. Response: {
    "message": "The route api/api/transfers could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
   
[2025-06-19 22:22:36] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:22:36] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:22:37] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:22:37] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:22:37] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:22:37] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:22:37] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:22:37] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transactions?per_page=10
[2025-06-19 22:22:37] [StrixBudget] [debug] Response received: HTTP 404, 8832 bytes
[2025-06-19 22:22:37] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transactions?per_page=10. Response: {
    "message": "The route api/api/transactions could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",

[2025-06-19 22:22:37] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transfers
[2025-06-19 22:22:37] [StrixBudget] [debug] Response received: HTTP 404, 8829 bytes
[2025-06-19 22:22:37] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transfers. Response: {
    "message": "The route api/api/transfers could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
   
[2025-06-19 22:22:39] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:22:39] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:22:40] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:22:40] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transactions?per_page=20
[2025-06-19 22:22:40] [StrixBudget] [debug] Response received: HTTP 404, 8832 bytes
[2025-06-19 22:22:40] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transactions?per_page=20. Response: {
    "message": "The route api/api/transactions could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",

[2025-06-19 22:22:40] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:22:40] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:22:40] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/counterparties
[2025-06-19 22:22:40] [StrixBudget] [debug] Response received: HTTP 404, 8834 bytes
[2025-06-19 22:22:40] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/counterparties. Response: {
    "message": "The route api/api/counterparties could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute"
[2025-06-19 22:22:40] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transaction-types
[2025-06-19 22:22:40] [StrixBudget] [debug] Response received: HTTP 404, 8837 bytes
[2025-06-19 22:22:40] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transaction-types. Response: {
    "message": "The route api/api/transaction-types could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRou
[2025-06-19 22:22:49] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:22:49] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:22:50] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:22:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:22:50] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:22:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/counterparties
[2025-06-19 22:22:50] [StrixBudget] [debug] Response received: HTTP 404, 8834 bytes
[2025-06-19 22:22:50] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/counterparties. Response: {
    "message": "The route api/api/counterparties could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute"
[2025-06-19 22:22:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transaction-types
[2025-06-19 22:22:50] [StrixBudget] [debug] Response received: HTTP 404, 8837 bytes
[2025-06-19 22:22:50] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transaction-types. Response: {
    "message": "The route api/api/transaction-types could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRou
[2025-06-19 22:22:57] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:22:57] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:22:58] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:22:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transactions?per_page=20
[2025-06-19 22:22:58] [StrixBudget] [debug] Response received: HTTP 404, 8832 bytes
[2025-06-19 22:22:58] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transactions?per_page=20. Response: {
    "message": "The route api/api/transactions could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",

[2025-06-19 22:22:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:22:58] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:22:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/counterparties
[2025-06-19 22:22:58] [StrixBudget] [debug] Response received: HTTP 404, 8834 bytes
[2025-06-19 22:22:58] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/counterparties. Response: {
    "message": "The route api/api/counterparties could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute"
[2025-06-19 22:22:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transaction-types
[2025-06-19 22:22:58] [StrixBudget] [debug] Response received: HTTP 404, 8837 bytes
[2025-06-19 22:22:58] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transaction-types. Response: {
    "message": "The route api/api/transaction-types could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRou
[2025-06-19 22:23:00] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:23:00] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:23:00] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:23:00] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:23:00] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:23:00] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:23:00] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:23:00] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transactions?per_page=10
[2025-06-19 22:23:00] [StrixBudget] [debug] Response received: HTTP 404, 8832 bytes
[2025-06-19 22:23:00] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transactions?per_page=10. Response: {
    "message": "The route api/api/transactions could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",

[2025-06-19 22:23:00] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transfers
[2025-06-19 22:23:00] [StrixBudget] [debug] Response received: HTTP 404, 8829 bytes
[2025-06-19 22:23:00] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transfers. Response: {
    "message": "The route api/api/transfers could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
   
[2025-06-19 22:23:02] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:23:02] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:23:02] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:23:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/counterparties?per_page=20
[2025-06-19 22:23:03] [StrixBudget] [debug] Response received: HTTP 404, 8834 bytes
[2025-06-19 22:23:03] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/counterparties?per_page=20. Response: {
    "message": "The route api/api/counterparties could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute"
[2025-06-19 22:23:06] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:23:06] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:23:06] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:23:06] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:23:43] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:23:43] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:23:43] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:23:43] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:23:47] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:23:47] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:23:47] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:23:47] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:23:55] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:23:55] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:23:55] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:23:55] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:24:13] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:24:13] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:24:13] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:24:13] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:24:37] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:24:37] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:24:37] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:24:37] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:24:37] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:24:37] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:24:37] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:24:37] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:24:43] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:24:43] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:24:43] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:24:43] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:24:43] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:24:43] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:24:43] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:24:43] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:24:48] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:24:48] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:24:48] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:24:48] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:24:48] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:24:48] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:24:48] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:24:48] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:24:55] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:24:55] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:24:55] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:24:55] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:24:55] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:24:55] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:24:55] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:24:55] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:25:01] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:25:01] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:25:01] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:25:01] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:25:19] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:25:19] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:25:19] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:25:19] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:25:19] [StrixBudget] [debug] Making real API request: POST http://***************/api/api/auth/login
[2025-06-19 22:25:19] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:25:19] [StrixBudget] [debug] Response received: HTTP 404, 8830 bytes
[2025-06-19 22:25:19] [StrixBudget] [error] HTTP error 404 for POST http://***************/api/api/auth/login. Response: {
    "message": "The route api/api/auth/login could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
  
[2025-06-19 22:28:23] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:28:23] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:28:23] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:28:23] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:28:23] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:28:23] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:28:23] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:28:23] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transactions?per_page=10
[2025-06-19 22:28:23] [StrixBudget] [debug] Response received: HTTP 404, 8832 bytes
[2025-06-19 22:28:23] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transactions?per_page=10. Response: {
    "message": "The route api/api/transactions could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",

[2025-06-19 22:28:23] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transfers
[2025-06-19 22:28:23] [StrixBudget] [debug] Response received: HTTP 404, 8829 bytes
[2025-06-19 22:28:23] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transfers. Response: {
    "message": "The route api/api/transfers could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
   
[2025-06-19 22:28:35] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:28:35] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:28:35] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:28:35] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 22:28:35] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:28:35] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:28:35] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:28:35] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/counterparties
[2025-06-19 22:28:35] [StrixBudget] [debug] Response received: HTTP 404, 8834 bytes
[2025-06-19 22:28:35] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/counterparties. Response: {
    "message": "The route api/api/counterparties could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute"
[2025-06-19 22:28:35] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transaction-types
[2025-06-19 22:28:35] [StrixBudget] [debug] Response received: HTTP 404, 8837 bytes
[2025-06-19 22:28:35] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transaction-types. Response: {
    "message": "The route api/api/transaction-types could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRou
[2025-06-19 22:28:41] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:28:41] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:28:41] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:28:41] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:28:41] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:28:41] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:28:41] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:28:41] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:28:41] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:28:41] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transfers
[2025-06-19 22:28:41] [StrixBudget] [debug] Response received: HTTP 404, 8829 bytes
[2025-06-19 22:28:41] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transfers. Response: {
    "message": "The route api/api/transfers could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
   
[2025-06-19 22:28:43] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:28:43] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:28:43] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:28:43] [StrixBudget] [debug] Making real API request: GET http://***************/api/api/transfers
[2025-06-19 22:28:43] [StrixBudget] [debug] Response received: HTTP 404, 8829 bytes
[2025-06-19 22:28:43] [StrixBudget] [error] HTTP error 404 for GET http://***************/api/api/transfers. Response: {
    "message": "The route api/api/transfers could not be found.",
    "exception": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException",
    "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php",
    "line": 45,
    "trace": [
        {
            "file": "/var/www/StrixBudget/vendor/laravel/framework/src/Illuminate/Routing/CompiledRouteCollection.php",
            "line": 143,
            "function": "handleMatchedRoute",
   
[2025-06-19 22:28:45] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:28:45] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:28:46] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:28:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:28:46] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:28:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:28:46] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:28:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:28:46] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:28:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:28:46] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:31:47] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:31:47] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:31:47] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:31:56] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:31:56] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:31:56] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:31:56] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:31:56] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:31:56] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:31:56] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:31:56] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:31:56] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:31:56] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:31:56] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:31:58] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:31:58] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:31:58] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:31:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 22:31:58] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:31:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:31:58] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:31:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 22:31:58] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:31:58] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:31:58] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 22:32:08] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:32:08] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:32:09] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:32:15] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:32:15] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:32:15] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:32:15] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties?per_page=20
[2025-06-19 22:32:15] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:32:20] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:32:20] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:32:20] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:32:20] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:32:20] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:32:20] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:32:20] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:32:20] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:32:20] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:32:20] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:32:20] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:35:24] [StrixBudget] [debug] Making real API request: POST https://api.strixbudget.com/api/auth/login
[2025-06-19 19:35:24] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"password"}
[2025-06-19 19:35:24] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for POST https://api.strixbudget.com/api/auth/login
[2025-06-19 19:35:24] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/api/transactions
[2025-06-19 19:35:24] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/api/transactions
[2025-06-19 19:35:24] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/api/transactions?per_page=5&type=income
[2025-06-19 19:35:24] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/api/transactions?per_page=5&type=income
[2025-06-19 19:35:24] [StrixBudget] [debug] Making real API request: GET https://api.strixbudget.com/api/transactions?per_page=10&from_date=2025-06-01&to_date=2025-06-19
[2025-06-19 19:35:24] [StrixBudget] [error] cURL error: Could not resolve host: api.strixbudget.com for GET https://api.strixbudget.com/api/transactions?per_page=10&from_date=2025-06-01&to_date=2025-06-19
[2025-06-19 22:35:47] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:35:47] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:35:47] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 19:35:56] [StrixBudget] [debug] Using mock mode for GET /transactions
[2025-06-19 19:35:56] [StrixBudget] [debug] Using mock mode for GET /transactions?per_page=5&type=income
[2025-06-19 19:35:56] [StrixBudget] [debug] Using mock mode for GET /transactions?per_page=10&from_date=2025-06-01&to_date=2025-06-19
[2025-06-19 19:39:20] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:39:20] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"password"}
[2025-06-19 19:39:20] [StrixBudget] [debug] Response received: HTTP 401, 49 bytes
[2025-06-19 19:39:20] [StrixBudget] [error] HTTP error 401 for POST http://***************/api/auth/login. Response: {"success":false,"message":"Invalid credentials"}
[2025-06-19 19:39:20] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions
[2025-06-19 19:39:20] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-19 19:39:20] [StrixBudget] [error] HTTP error 401 for GET http://***************/api/transactions. Response: {"message":"Unauthenticated."}
[2025-06-19 19:39:20] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=5
[2025-06-19 19:39:20] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-19 19:39:20] [StrixBudget] [error] HTTP error 401 for GET http://***************/api/transactions?per_page=5. Response: {"message":"Unauthenticated."}
[2025-06-19 19:39:54] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:39:54] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"admin123"}
[2025-06-19 19:39:54] [StrixBudget] [debug] Response received: HTTP 401, 49 bytes
[2025-06-19 19:39:54] [StrixBudget] [error] HTTP error 401 for POST http://***************/api/auth/login. Response: {"success":false,"message":"Invalid credentials"}
[2025-06-19 19:39:54] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:39:54] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"test123"}
[2025-06-19 19:39:54] [StrixBudget] [debug] Response received: HTTP 401, 49 bytes
[2025-06-19 19:39:54] [StrixBudget] [error] HTTP error 401 for POST http://***************/api/auth/login. Response: {"success":false,"message":"Invalid credentials"}
[2025-06-19 19:39:54] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:39:54] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"admin"}
[2025-06-19 19:39:54] [StrixBudget] [debug] Response received: HTTP 401, 49 bytes
[2025-06-19 19:39:54] [StrixBudget] [error] HTTP error 401 for POST http://***************/api/auth/login. Response: {"success":false,"message":"Invalid credentials"}
[2025-06-19 19:39:54] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:39:54] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"password"}
[2025-06-19 19:39:54] [StrixBudget] [debug] Response received: HTTP 401, 49 bytes
[2025-06-19 19:39:54] [StrixBudget] [error] HTTP error 401 for POST http://***************/api/auth/login. Response: {"success":false,"message":"Invalid credentials"}
[2025-06-19 19:39:54] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:39:54] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"demo"}
[2025-06-19 19:39:54] [StrixBudget] [debug] Response received: HTTP 401, 49 bytes
[2025-06-19 19:39:54] [StrixBudget] [error] HTTP error 401 for POST http://***************/api/auth/login. Response: {"success":false,"message":"Invalid credentials"}
[2025-06-19 19:39:54] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions
[2025-06-19 19:39:54] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-19 19:39:54] [StrixBudget] [error] HTTP error 401 for GET http://***************/api/transactions. Response: {"message":"Unauthenticated."}
[2025-06-19 19:39:54] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=5
[2025-06-19 19:39:54] [StrixBudget] [debug] Response received: HTTP 401, 30 bytes
[2025-06-19 19:39:54] [StrixBudget] [error] HTTP error 401 for GET http://***************/api/transactions?per_page=5. Response: {"message":"Unauthenticated."}
[2025-06-19 22:40:25] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:40:25] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:40:26] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 19:41:21] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:41:21] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:41:21] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 19:41:21] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions
[2025-06-19 19:41:21] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:41:21] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=5
[2025-06-19 19:41:21] [StrixBudget] [debug] Response received: HTTP 200, 2939 bytes
[2025-06-19 19:42:22] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:42:22] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:42:22] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 19:42:22] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions
[2025-06-19 19:42:22] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:42:22] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=5
[2025-06-19 19:42:22] [StrixBudget] [debug] Response received: HTTP 200, 2939 bytes
[2025-06-19 22:43:03] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:43:03] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:43:03] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:43:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:43:03] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:43:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:43:03] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:43:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:43:03] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:43:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:43:03] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:43:13] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:43:13] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:43:14] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:43:14] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 22:43:14] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:43:14] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:43:14] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:43:14] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 22:43:14] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:43:14] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:43:14] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 22:45:25] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:45:26] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:45:26] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:45:26] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 22:45:26] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:45:26] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:45:26] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:45:26] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 22:45:26] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:45:26] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:45:26] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 22:45:45] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:45:45] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:45:46] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:45:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:45:46] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:45:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:45:46] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:45:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:45:46] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:45:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:45:46] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:45:54] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:45:54] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:45:54] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 19:45:54] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions
[2025-06-19 19:45:54] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:45:54] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=1
[2025-06-19 19:45:54] [StrixBudget] [debug] Response received: HTTP 200, 1947 bytes
[2025-06-19 19:45:54] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?type=income
[2025-06-19 19:45:54] [StrixBudget] [debug] Response received: HTTP 200, 1766 bytes
[2025-06-19 19:45:54] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?type=expense
[2025-06-19 19:45:54] [StrixBudget] [debug] Response received: HTTP 200, 1764 bytes
[2025-06-19 19:46:34] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:46:34] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:46:34] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 19:46:34] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions
[2025-06-19 19:46:34] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 19:46:34] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=1
[2025-06-19 19:46:34] [StrixBudget] [debug] Response received: HTTP 200, 1947 bytes
[2025-06-19 19:46:34] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?type=income
[2025-06-19 19:46:34] [StrixBudget] [debug] Response received: HTTP 200, 1766 bytes
[2025-06-19 19:46:34] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?type=expense
[2025-06-19 19:46:34] [StrixBudget] [debug] Response received: HTTP 200, 1764 bytes
[2025-06-19 22:46:49] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:46:49] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:46:50] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:46:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 22:46:50] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:46:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:46:50] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:46:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 22:46:50] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:46:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:46:50] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 22:47:03] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:47:03] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:47:04] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:47:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:47:04] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:47:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:47:04] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:47:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:47:04] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:47:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:47:04] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:48:29] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:48:29] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:48:29] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 19:48:29] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 19:48:29] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 19:48:29] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:48:29] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:48:29] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 19:48:29] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 19:48:29] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:48:29] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:48:29] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/user
[2025-06-19 19:48:29] [StrixBudget] [debug] Response received: HTTP 200, 287 bytes
[2025-06-19 22:50:48] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:50:48] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:50:48] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:50:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:50:48] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:50:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:50:48] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:50:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:50:48] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:50:48] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:50:48] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:50:52] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:50:52] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:50:52] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:50:52] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties?per_page=20
[2025-06-19 22:50:52] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:50:55] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:50:55] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:50:56] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:50:56] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:50:56] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:50:56] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:50:56] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:50:56] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:50:56] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:50:56] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:50:56] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:50:59] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:50:59] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:51:00] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:51:00] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:51:00] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 22:51:02] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:51:02] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:51:03] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:51:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:51:03] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:51:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:51:03] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:51:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:51:03] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:51:03] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:51:03] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:51:04] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:51:04] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:51:05] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:51:05] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:51:05] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:51:09] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:51:09] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:51:09] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:51:09] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:51:09] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:51:09] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:51:09] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:51:09] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:51:09] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:51:09] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:51:09] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:51:11] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:51:11] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:51:11] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:51:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 22:51:11] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:51:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:51:11] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:51:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 22:51:11] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:51:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:51:11] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 22:51:13] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:51:13] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:51:14] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:51:14] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:51:14] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:51:14] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:51:14] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:51:14] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:51:14] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:51:14] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:51:14] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:51:24] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 19:51:24] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 19:51:24] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 19:51:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 19:51:24] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 19:51:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:51:24] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:51:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 19:51:24] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 19:51:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:51:24] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:51:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 19:51:24] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 19:51:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 19:51:24] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 19:51:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 19:51:24] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 19:51:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 19:51:24] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 19:51:24] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions
[2025-06-19 19:51:24] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:51:39] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:51:39] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:51:39] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:51:39] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties?per_page=20
[2025-06-19 22:51:39] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:51:46] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:51:46] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:51:46] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:51:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:51:46] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:51:50] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:51:50] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:51:50] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:51:50] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:51:50] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 22:51:56] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:51:56] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:51:57] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:51:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:51:57] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:51:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:51:57] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:51:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:51:57] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:51:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:51:57] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:52:11] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:52:11] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:52:11] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:52:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:52:11] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:52:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 22:52:11] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:52:11] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:52:11] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 22:52:38] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:52:38] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:52:39] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:52:39] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:52:39] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:52:39] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 22:52:39] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:52:39] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:52:39] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 22:53:18] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:53:18] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:53:18] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:53:32] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:53:32] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:53:32] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:53:32] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:53:32] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:53:32] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:53:32] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:53:32] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:53:32] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:53:32] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:53:32] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:55:52] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:55:52] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:55:52] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:55:52] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 22:55:52] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:55:52] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:55:52] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:55:52] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 22:55:52] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:55:52] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:55:52] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 22:55:57] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:55:57] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:55:57] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:55:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 22:55:57] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 22:55:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:55:57] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:55:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 22:55:57] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:55:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 22:55:57] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 22:57:46] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 22:57:46] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 22:57:46] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 22:57:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 22:57:46] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 22:57:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 22:57:46] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 22:57:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 22:57:46] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 22:57:46] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 22:57:46] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 23:02:08] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:02:08] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:02:09] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:02:16] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:02:16] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:02:16] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:02:22] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:02:22] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:02:22] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:02:22] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 23:02:22] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 23:02:22] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 23:02:22] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 23:02:22] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 23:02:22] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 23:02:22] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 23:02:22] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 23:02:30] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:02:30] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:02:30] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:02:35] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:02:35] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:02:36] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:04:17] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:04:17] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:04:17] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:04:17] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties?per_page=20
[2025-06-19 23:04:17] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 23:04:22] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:04:22] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:04:22] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:04:22] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 23:04:22] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 23:04:22] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 23:04:22] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 23:04:22] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 23:04:22] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 23:04:22] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 23:04:22] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 23:04:29] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:04:29] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:04:29] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:04:33] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:04:33] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:04:33] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:04:33] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 23:04:33] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 23:04:33] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 23:04:33] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 23:04:33] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 23:04:33] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 23:04:33] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 23:04:33] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 23:04:46] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:04:46] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:04:47] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:04:53] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:04:53] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:04:54] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:04:54] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 23:04:54] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 23:04:54] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 23:04:54] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 23:04:54] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 23:04:54] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 23:04:54] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 23:04:54] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 23:04:57] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:04:57] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:04:57] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:04:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 23:04:57] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 23:04:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 23:04:57] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 23:04:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 23:04:57] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 23:04:57] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 23:04:57] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 23:05:00] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:05:00] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:05:00] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:05:00] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=20
[2025-06-19 23:05:00] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 23:05:00] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 23:05:00] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 23:05:00] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties
[2025-06-19 23:05:00] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 23:05:00] [StrixBudget] [debug] Making real API request: GET http://***************/api/transaction-types
[2025-06-19 23:05:00] [StrixBudget] [debug] Response received: HTTP 200, 1081 bytes
[2025-06-19 23:05:04] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:05:04] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:05:04] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:05:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 23:05:04] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 23:05:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 23:05:04] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 23:05:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 23:05:04] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 23:05:04] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 23:05:04] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 23:05:06] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:05:06] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:05:06] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:05:06] [StrixBudget] [debug] Making real API request: GET http://***************/api/counterparties?per_page=20
[2025-06-19 23:05:06] [StrixBudget] [debug] Response received: HTTP 200, 1502 bytes
[2025-06-19 23:05:08] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:05:08] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:05:09] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-19 23:05:09] [StrixBudget] [debug] Making real API request: GET http://***************/api/auth/statistics
[2025-06-19 23:05:09] [StrixBudget] [debug] Response received: HTTP 200, 303 bytes
[2025-06-19 23:05:09] [StrixBudget] [debug] Making real API request: GET http://***************/api/bank-accounts
[2025-06-19 23:05:09] [StrixBudget] [debug] Response received: HTTP 200, 824 bytes
[2025-06-19 23:05:09] [StrixBudget] [debug] Making real API request: GET http://***************/api/transactions?per_page=10
[2025-06-19 23:05:09] [StrixBudget] [debug] Response received: HTTP 200, 2940 bytes
[2025-06-19 23:05:09] [StrixBudget] [debug] Making real API request: GET http://***************/api/transfers
[2025-06-19 23:05:09] [StrixBudget] [debug] Response received: HTTP 200, 1610 bytes
[2025-06-19 23:05:10] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-19 23:05:10] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-19 23:05:11] [StrixBudget] [debug] Response received: HTTP 200, 370 bytes
[2025-06-29 20:29:10] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-29 20:29:10] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-29 20:29:13] [StrixBudget] [error] cURL error: Failed to connect to *************** port 80 after 3055 ms: Couldn't connect to server for POST http://***************/api/auth/login
[2025-06-29 20:29:21] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-29 20:29:21] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-29 20:29:24] [StrixBudget] [error] cURL error: Failed to connect to *************** port 80 after 3067 ms: Couldn't connect to server for POST http://***************/api/auth/login
[2025-06-29 20:29:31] [StrixBudget] [debug] Making real API request: POST http://***************/api/auth/login
[2025-06-29 20:29:31] [StrixBudget] [debug] Request data: {"email":"<EMAIL>","password":"kok0lin0"}
[2025-06-29 20:29:34] [StrixBudget] [error] cURL error: Failed to connect to *************** port 80 after 3074 ms: Couldn't connect to server for POST http://***************/api/auth/login
